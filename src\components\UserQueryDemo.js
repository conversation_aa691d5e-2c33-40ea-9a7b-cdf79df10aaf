"use strict";
// 用户查询演示组件
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var UserQueryDemo = function (_a) {
    var _b = _a.className, className = _b === void 0 ? '' : _b;
    var _c = (0, react_1.useState)('social_network'), graphName = _c[0], setGraphName = _c[1];
    var _d = (0, react_1.useState)('User'), userVertexType = _d[0], setUserVertexType = _d[1];
    var _e = (0, react_1.useState)('Friend'), relationEdgeType = _e[0], setRelationEdgeType = _e[1];
    // 查询状态
    var _f = (0, react_1.useState)(false), loading = _f[0], setLoading = _f[1];
    var _g = (0, react_1.useState)(''), error = _g[0], setError = _g[1];
    // 用户名查询
    var _h = (0, react_1.useState)(''), username = _h[0], setUsername = _h[1];
    var _j = (0, react_1.useState)([]), foundUsers = _j[0], setFoundUsers = _j[1];
    // 路径查询
    var _k = (0, react_1.useState)(''), startUserId = _k[0], setStartUserId = _k[1];
    var _l = (0, react_1.useState)(''), targetUserId = _l[0], setTargetUserId = _l[1];
    var _m = (0, react_1.useState)(3), maxDepth = _m[0], setMaxDepth = _m[1];
    var _o = (0, react_1.useState)([]), pathResults = _o[0], setPathResults = _o[1];
    // 相似用户推荐
    var _p = (0, react_1.useState)(''), recommendUserId = _p[0], setRecommendUserId = _p[1];
    var _q = (0, react_1.useState)(10), topN = _q[0], setTopN = _q[1];
    var _r = (0, react_1.useState)([]), similarUsers = _r[0], setSimilarUsers = _r[1];
    // 模拟查询服务方法
    var simulateQuery = function (queryType, params) { return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            // 这里暂时返回模拟数据，等 UserQueryService 修复后再替换
            console.log("\u6A21\u62DF\u67E5\u8BE2: ".concat(queryType), params);
            if (queryType === 'userByName') {
                return [2 /*return*/, [{ id: 1, name: params.username, properties: { age: 25 } }]];
            }
            else if (queryType === 'path') {
                return [2 /*return*/, [{ path: [params.startId, params.targetId || params.startId + 1], depth: 1, edges: [] }]];
            }
            else if (queryType === 'similar') {
                return [2 /*return*/, [{ id: 2, score: 0.85, properties: { name: 'Similar User' } }]];
            }
            return [2 /*return*/, []];
        });
    }); };
    // 用户名查询
    var handleUserNameQuery = function () { return __awaiter(void 0, void 0, void 0, function () {
        var users, err_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!username.trim()) {
                        setError('请输入用户名');
                        return [2 /*return*/];
                    }
                    setLoading(true);
                    setError('');
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, 4, 5]);
                    return [4 /*yield*/, simulateQuery('userByName', { username: username })];
                case 2:
                    users = _a.sent();
                    setFoundUsers(users);
                    if (users.length === 0) {
                        setError("\u672A\u627E\u5230\u7528\u6237: ".concat(username));
                    }
                    return [3 /*break*/, 5];
                case 3:
                    err_1 = _a.sent();
                    setError("\u67E5\u8BE2\u5931\u8D25: ".concat(err_1));
                    return [3 /*break*/, 5];
                case 4:
                    setLoading(false);
                    return [7 /*endfinally*/];
                case 5: return [2 /*return*/];
            }
        });
    }); };
    // 路径查询
    var handlePathQuery = function () { return __awaiter(void 0, void 0, void 0, function () {
        var startId, targetId, paths, err_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!startUserId.trim()) {
                        setError('请输入起始用户ID');
                        return [2 /*return*/];
                    }
                    setLoading(true);
                    setError('');
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, 4, 5]);
                    startId = parseInt(startUserId);
                    targetId = targetUserId.trim() ? parseInt(targetUserId) : undefined;
                    return [4 /*yield*/, simulateQuery('path', { startId: startId, targetId: targetId, maxDepth: maxDepth })];
                case 2:
                    paths = _a.sent();
                    setPathResults(paths);
                    if (paths.length === 0) {
                        setError('未找到路径');
                    }
                    return [3 /*break*/, 5];
                case 3:
                    err_2 = _a.sent();
                    setError("\u8DEF\u5F84\u67E5\u8BE2\u5931\u8D25: ".concat(err_2));
                    return [3 /*break*/, 5];
                case 4:
                    setLoading(false);
                    return [7 /*endfinally*/];
                case 5: return [2 /*return*/];
            }
        });
    }); };
    // 相似用户推荐
    var handleSimilarUserQuery = function () { return __awaiter(void 0, void 0, void 0, function () {
        var userId, recommendations, err_3;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!recommendUserId.trim()) {
                        setError('请输入用户ID');
                        return [2 /*return*/];
                    }
                    setLoading(true);
                    setError('');
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, 4, 5]);
                    userId = parseInt(recommendUserId);
                    return [4 /*yield*/, simulateQuery('similar', { userId: userId, topN: topN })];
                case 2:
                    recommendations = _a.sent();
                    setSimilarUsers(recommendations);
                    if (recommendations.length === 0) {
                        setError('未找到相似用户');
                    }
                    return [3 /*break*/, 5];
                case 3:
                    err_3 = _a.sent();
                    setError("\u63A8\u8350\u67E5\u8BE2\u5931\u8D25: ".concat(err_3));
                    return [3 /*break*/, 5];
                case 4:
                    setLoading(false);
                    return [7 /*endfinally*/];
                case 5: return [2 /*return*/];
            }
        });
    }); };
    // 复合查询：用户名 + 推荐
    var handleComplexQuery = function () { return __awaiter(void 0, void 0, void 0, function () {
        var users, recommendations, err_4;
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    if (!username.trim()) {
                        setError('请输入用户名');
                        return [2 /*return*/];
                    }
                    setLoading(true);
                    setError('');
                    _b.label = 1;
                case 1:
                    _b.trys.push([1, 4, 5, 6]);
                    return [4 /*yield*/, simulateQuery('userByName', { username: username })];
                case 2:
                    users = _b.sent();
                    return [4 /*yield*/, simulateQuery('similar', { userId: (_a = users[0]) === null || _a === void 0 ? void 0 : _a.id, topN: topN })];
                case 3:
                    recommendations = _b.sent();
                    setFoundUsers(users);
                    setSimilarUsers(recommendations);
                    if (users.length > 0) {
                        setRecommendUserId(users[0].id.toString());
                    }
                    return [3 /*break*/, 6];
                case 4:
                    err_4 = _b.sent();
                    setError("\u590D\u5408\u67E5\u8BE2\u5931\u8D25: ".concat(err_4));
                    return [3 /*break*/, 6];
                case 5:
                    setLoading(false);
                    return [7 /*endfinally*/];
                case 6: return [2 /*return*/];
            }
        });
    }); };
    return (<div className={"p-6 bg-primary-dark rounded-lg shadow-lg border border-gray-800 ".concat(className)}>
      <h2 className="text-spec-header font-bold mb-6 text-text-primary font-arial">用户查询演示</h2>

      {/* 配置区域 */}
      <div className="mb-6 p-4 bg-gray-900 rounded border border-gray-700">
        <h3 className="text-spec-body font-semibold mb-3 text-text-primary font-arial">图配置</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-text-secondary text-spec-regular font-arial mb-1">图名称</label>
            <input type="text" value={graphName} onChange={function (e) { return setGraphName(e.target.value); }} className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"/>
          </div>
          <div>
            <label className="block text-text-secondary text-spec-regular font-arial mb-1">用户顶点类型</label>
            <input type="text" value={userVertexType} onChange={function (e) { return setUserVertexType(e.target.value); }} className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"/>
          </div>
          <div>
            <label className="block text-text-secondary text-spec-regular font-arial mb-1">关系边类型</label>
            <input type="text" value={relationEdgeType} onChange={function (e) { return setRelationEdgeType(e.target.value); }} className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"/>
          </div>
        </div>
      </div>

      {/* 错误信息 */}
      {error && (<div className="mb-4 p-3 bg-primary-orange/10 border border-primary-orange/30 rounded text-primary-orange font-arial">
          {error}
        </div>)}

      {/* 加载状态 */}
      {loading && (<div className="mb-4 p-3 bg-primary-blue/10 border border-primary-blue/30 rounded text-primary-blue font-arial">
          查询中...
        </div>)}

      {/* 查询区域 */}
      <div className="space-y-6">
        {/* 1. 用户名查询 */}
        <div className="p-4 bg-gray-900 rounded border border-gray-700">
          <h3 className="text-spec-body font-semibold mb-3 text-text-primary font-arial">1. 用户名查询</h3>
          <div className="flex gap-4 mb-4">
            <input type="text" placeholder="输入用户名" value={username} onChange={function (e) { return setUsername(e.target.value); }} className="flex-1 p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"/>
            <button onClick={handleUserNameQuery} disabled={loading} className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-arial font-semibold disabled:opacity-50">
              查询用户
            </button>
            <button onClick={handleComplexQuery} disabled={loading} className="px-4 py-2 bg-primary-yellow text-primary-dark rounded hover:bg-primary-yellow/80 transition-colors duration-200 font-arial font-semibold disabled:opacity-50">
              查询+推荐
            </button>
          </div>
          
          {foundUsers.length > 0 && (<div>
              <h4 className="text-spec-regular font-semibold mb-2 text-text-primary font-arial">找到的用户:</h4>
              <div className="space-y-2">
                {foundUsers.map(function (user) { return (<div key={user.id} className="p-2 bg-primary-yellow/10 border border-primary-yellow/30 rounded">
                    <span className="text-primary-yellow font-arial">ID: {user.id}</span>
                    <span className="text-text-primary font-arial ml-4">姓名: {user.name}</span>
                  </div>); })}
              </div>
            </div>)}
        </div>

        {/* 2. 路径查询 */}
        <div className="p-4 bg-gray-900 rounded border border-gray-700">
          <h3 className="text-spec-body font-semibold mb-3 text-text-primary font-arial">2. 最大深度路径查询</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <input type="number" placeholder="起始用户ID" value={startUserId} onChange={function (e) { return setStartUserId(e.target.value); }} className="p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"/>
            <input type="number" placeholder="目标用户ID (可选)" value={targetUserId} onChange={function (e) { return setTargetUserId(e.target.value); }} className="p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"/>
            <input type="number" placeholder="最大深度" value={maxDepth} onChange={function (e) { return setMaxDepth(parseInt(e.target.value) || 3); }} className="p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"/>
            <button onClick={handlePathQuery} disabled={loading} className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-arial font-semibold disabled:opacity-50">
              查询路径
            </button>
          </div>
          
          {pathResults.length > 0 && (<div>
              <h4 className="text-spec-regular font-semibold mb-2 text-text-primary font-arial">路径结果:</h4>
              <div className="space-y-2">
                {pathResults.map(function (path, index) { return (<div key={index} className="p-2 bg-primary-orange/10 border border-primary-orange/30 rounded">
                    <span className="text-primary-orange font-arial">深度: {path.depth}</span>
                    <span className="text-text-primary font-arial ml-4">路径: {path.path.join(' → ')}</span>
                  </div>); })}
              </div>
            </div>)}
        </div>

        {/* 3. 相似用户推荐 */}
        <div className="p-4 bg-gray-900 rounded border border-gray-700">
          <h3 className="text-spec-body font-semibold mb-3 text-text-primary font-arial">3. 相似用户推荐</h3>
          <div className="flex gap-4 mb-4">
            <input type="number" placeholder="用户ID" value={recommendUserId} onChange={function (e) { return setRecommendUserId(e.target.value); }} className="flex-1 p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"/>
            <input type="number" placeholder="推荐数量" value={topN} onChange={function (e) { return setTopN(parseInt(e.target.value) || 10); }} className="w-32 p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"/>
            <button onClick={handleSimilarUserQuery} disabled={loading} className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-arial font-semibold disabled:opacity-50">
              推荐用户
            </button>
          </div>
          
          {similarUsers.length > 0 && (<div>
              <h4 className="text-spec-regular font-semibold mb-2 text-text-primary font-arial">推荐结果:</h4>
              <div className="space-y-2">
                {similarUsers.map(function (user) {
                var _a;
                return (<div key={user.id} className="p-2 bg-primary-yellow/10 border border-primary-yellow/30 rounded">
                    <span className="text-primary-yellow font-arial">ID: {user.id}</span>
                    <span className="text-text-primary font-arial ml-4">相似度: {user.score.toFixed(4)}</span>
                    {((_a = user.properties) === null || _a === void 0 ? void 0 : _a.name) && (<span className="text-text-secondary font-arial ml-4">姓名: {user.properties.name}</span>)}
                  </div>);
            })}
              </div>
            </div>)}
        </div>
      </div>
    </div>);
};
exports.default = UserQueryDemo;
