"use strict";
// 用户搜索自动完成组件
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var graph_1 = require("../services/graph");
var UserSearchAutocomplete = function (_a) {
    var value = _a.value, onChange = _a.onChange, _b = _a.placeholder, placeholder = _b === void 0 ? "请输入用户名搜索" : _b, _c = _a.disabled, disabled = _c === void 0 ? false : _c, _d = _a.className, className = _d === void 0 ? "" : _d;
    var _e = (0, react_1.useState)(''), inputValue = _e[0], setInputValue = _e[1];
    var _f = (0, react_1.useState)([]), suggestions = _f[0], setSuggestions = _f[1];
    var _g = (0, react_1.useState)(false), isOpen = _g[0], setIsOpen = _g[1];
    var _h = (0, react_1.useState)(false), loading = _h[0], setLoading = _h[1];
    var _j = (0, react_1.useState)(-1), highlightedIndex = _j[0], setHighlightedIndex = _j[1];
    var inputRef = (0, react_1.useRef)(null);
    var dropdownRef = (0, react_1.useRef)(null);
    var debounceRef = (0, react_1.useRef)();
    // Mock用户数据 - 使用数据编号+ID格式
    var mockUsers = [
        { id: 1, name: "数据编号001", properties: { type: 'ego', department: '技术部', level: '高级' } },
        { id: 2, name: "数据编号002", properties: { type: 'ego', department: '技术部', level: '高级' } },
        { id: 3, name: "数据编号003", properties: { type: 'ego', department: '技术部', level: '高级' } },
        { id: 4, name: "数据编号004", properties: { type: 'ego', department: '技术部', level: '高级' } },
        { id: 5, name: "数据编号005", properties: { type: 'ego', department: '技术部', level: '高级' } },
        { id: 6, name: "数据编号006", properties: { type: 'ego', department: '产品部', level: '中级' } },
        { id: 7, name: "数据编号007", properties: { type: 'ego', department: '产品部', level: '中级' } },
        { id: 8, name: "数据编号008", properties: { type: 'ego', department: '产品部', level: '中级' } },
        { id: 9, name: "数据编号009", properties: { type: 'ego', department: '产品部', level: '中级' } },
        { id: 10, name: "数据编号010", properties: { type: 'ego', department: '产品部', level: '中级' } },
        { id: 11, name: "数据编号011", properties: { type: 'ego', department: '设计部', level: '中级' } },
        { id: 12, name: "数据编号012", properties: { type: 'ego', department: '设计部', level: '中级' } },
        { id: 13, name: "数据编号013", properties: { type: 'ego', department: '设计部', level: '中级' } },
        { id: 14, name: "数据编号014", properties: { type: 'ego', department: '设计部', level: '中级' } },
        { id: 15, name: "数据编号015", properties: { type: 'ego', department: '设计部', level: '中级' } },
        { id: 16, name: "数据编号016", properties: { type: 'ego', department: '运营部', level: '初级' } },
        { id: 17, name: "数据编号017", properties: { type: 'ego', department: '运营部', level: '初级' } },
        { id: 18, name: "数据编号018", properties: { type: 'ego', department: '运营部', level: '初级' } },
        { id: 19, name: "数据编号019", properties: { type: 'ego', department: '运营部', level: '初级' } },
        { id: 20, name: "数据编号020", properties: { type: 'ego', department: '运营部', level: '初级' } }
    ];
    // 当外部 value 变化时，更新输入框显示
    (0, react_1.useEffect)(function () {
        if (value) {
            setInputValue(value.name);
        }
        else {
            setInputValue('');
        }
    }, [value]);
    // Mock用户搜索方法
    var mockUserSearch = (0, react_1.useCallback)(function (searchTerm) {
        if (searchTerm.trim().length < 1) {
            setSuggestions([]);
            setIsOpen(false);
            return;
        }
        setLoading(true);
        // 模拟网络延迟
        setTimeout(function () {
            // 根据搜索词过滤用户，如果搜索词为空则返回所有用户
            var filteredUsers = mockUsers.filter(function (user) {
                return user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    user.id.toString().includes(searchTerm);
            });
            // 如果没有匹配的用户，返回前10个用户作为默认结果
            var resultUsers = filteredUsers.length > 0 ? filteredUsers : mockUsers.slice(0, 10);
            setSuggestions(resultUsers.slice(0, 10)); // 最多显示10个
            setIsOpen(resultUsers.length > 0);
            setHighlightedIndex(-1);
            setLoading(false);
        }, 200); // 模拟200ms网络延迟
    }, [mockUsers]);
    // 防抖搜索函数 - 使用 simplePageQuery
    var debouncedSearch = (0, react_1.useCallback)(function (searchTerm) { return __awaiter(void 0, void 0, void 0, function () {
        var graphs, graphName, queryReq, result, queryError_1, users, _i, _a, id, error_1, userQueryService, users, fallbackError_1;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    if (searchTerm.trim().length < 1) {
                        setSuggestions([]);
                        setIsOpen(false);
                        return [2 /*return*/];
                    }
                    setLoading(true);
                    _b.label = 1;
                case 1:
                    _b.trys.push([1, 7, 13, 14]);
                    console.log('搜索用户:', searchTerm);
                    return [4 /*yield*/, graph_1.default.getGraphs()];
                case 2:
                    graphs = _b.sent();
                    graphName = Array.isArray(graphs) && graphs.length > 0 ? graphs[0] : 'default';
                    queryReq = {
                        Ontology: graphName,
                        veName: 'User', // 假设用户节点类型为 User
                        vertexFlag: true, // 查询节点
                        filter: {
                            filterMap: {
                                name: searchTerm // 按名称模糊搜索
                            },
                            targetFilter: null
                        },
                        pageSize: 10, // 最多返回10个结果
                        scrollId: -1 // 第一页
                    };
                    console.log('simplePageQuery 请求:', queryReq);
                    result = void 0;
                    _b.label = 3;
                case 3:
                    _b.trys.push([3, 5, , 6]);
                    return [4 /*yield*/, graph_1.default.simplePageQuery(queryReq)];
                case 4:
                    result = _b.sent();
                    console.log('simplePageQuery 结果:', result);
                    console.log('结果类型:', typeof result);
                    console.log('结果 JSON:', JSON.stringify(result, null, 2));
                    return [3 /*break*/, 6];
                case 5:
                    queryError_1 = _b.sent();
                    console.error('simplePageQuery 执行失败:', queryError_1);
                    throw queryError_1;
                case 6:
                    users = [];
                    if (result.ids && Array.isArray(result.ids) && result.ids.length > 0) {
                        console.log('处理用户 IDs:', result.ids);
                        // 现在 IDs 应该已经是数字了
                        for (_i = 0, _a = result.ids.slice(0, 10); _i < _a.length; _i++) { // 限制最多10个
                            id = _a[_i];
                            if (typeof id === 'number') {
                                users.push({
                                    id: id,
                                    name: "\u7528\u6237 ".concat(id),
                                    properties: {}
                                });
                            }
                            else {
                                console.warn('ID 仍然不是数字格式:', id, typeof id);
                            }
                        }
                    }
                    else {
                        console.log('没有找到用户 IDs 或格式不正确');
                    }
                    console.log('转换后的用户列表:', users);
                    setSuggestions(users);
                    setIsOpen(users.length > 0);
                    setHighlightedIndex(-1);
                    return [3 /*break*/, 14];
                case 7:
                    error_1 = _b.sent();
                    console.error('simplePageQuery 用户搜索失败:', error_1);
                    _b.label = 8;
                case 8:
                    _b.trys.push([8, 11, , 12]);
                    console.log('尝试使用 userQueryService.queryUserByName 作为回退方案');
                    return [4 /*yield*/, Promise.resolve().then(function () { return require('../services/graph/UserQueryService'); })];
                case 9:
                    userQueryService = (_b.sent()).userQueryService;
                    return [4 /*yield*/, userQueryService.queryUserByName(searchTerm)];
                case 10:
                    users = _b.sent();
                    console.log('回退查询结果:', users);
                    setSuggestions(users);
                    setIsOpen(users.length > 0);
                    setHighlightedIndex(-1);
                    return [3 /*break*/, 12];
                case 11:
                    fallbackError_1 = _b.sent();
                    console.error('回退查询也失败:', fallbackError_1);
                    setSuggestions([]);
                    setIsOpen(false);
                    return [3 /*break*/, 12];
                case 12: return [3 /*break*/, 14];
                case 13:
                    setLoading(false);
                    return [7 /*endfinally*/];
                case 14: return [2 /*return*/];
            }
        });
    }); }, []);
    // 处理输入变化
    var handleInputChange = function (e) {
        var newValue = e.target.value;
        setInputValue(newValue);
        // 如果输入值变化，清除当前选择
        if (value && newValue !== value.name) {
            onChange(null);
        }
        // 清除之前的防抖定时器
        if (debounceRef.current) {
            clearTimeout(debounceRef.current);
        }
        // 设置新的防抖定时器
        debounceRef.current = setTimeout(function () {
            // debouncedSearch(newValue);
            // mock数据
            mockUserSearch(newValue);
        }, 300);
    };
    // 处理选择用户
    var handleSelectUser = function (user) {
        setInputValue(user.name);
        setSuggestions([]);
        setIsOpen(false);
        setHighlightedIndex(-1);
        onChange(user);
    };
    // 处理键盘事件
    var handleKeyDown = function (e) {
        if (!isOpen || suggestions.length === 0)
            return;
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setHighlightedIndex(function (prev) {
                    return prev < suggestions.length - 1 ? prev + 1 : 0;
                });
                break;
            case 'ArrowUp':
                e.preventDefault();
                setHighlightedIndex(function (prev) {
                    return prev > 0 ? prev - 1 : suggestions.length - 1;
                });
                break;
            case 'Enter':
                e.preventDefault();
                if (highlightedIndex >= 0 && highlightedIndex < suggestions.length) {
                    handleSelectUser(suggestions[highlightedIndex]);
                }
                break;
            case 'Escape':
                setIsOpen(false);
                setHighlightedIndex(-1);
                break;
        }
    };
    // 处理失去焦点
    var handleBlur = function () {
        // 延迟关闭，允许点击下拉项
        setTimeout(function () {
            setIsOpen(false);
            setHighlightedIndex(-1);
        }, 200);
    };
    // 处理获得焦点
    var handleFocus = function () {
        if (suggestions.length > 0) {
            setIsOpen(true);
        }
    };
    // 清理定时器
    (0, react_1.useEffect)(function () {
        return function () {
            if (debounceRef.current) {
                clearTimeout(debounceRef.current);
            }
        };
    }, []);
    return (<div className={"relative ".concat(className)}>
      {/* 输入框 */}
      <div className="relative">
        <input ref={inputRef} type="text" value={inputValue} onChange={handleInputChange} onKeyDown={handleKeyDown} onBlur={handleBlur} onFocus={handleFocus} placeholder={placeholder} disabled={disabled} className="w-full h-[38px] px-4 pr-10 bg-transparent border-2 border-white rounded-[5px] text-white placeholder-[#d9d9d9] focus:outline-none focus:border-[#0090ea] disabled:opacity-50" style={{ fontFamily: 'PingFang SC' }}/>
        
        {/* 加载指示器 */}
        {loading && (<div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>)}
        
        {/* 下拉箭头 */}
        {!loading && (<div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white">
            <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </div>)}
      </div>

      {/* 下拉列表 */}
      {isOpen && suggestions.length > 0 && (<div ref={dropdownRef} className="absolute z-50 w-full mt-1 bg-gray-800 border border-gray-600 rounded-[5px] shadow-lg max-h-60 overflow-y-auto">
          {suggestions.map(function (user, index) { return (<div key={user.id} className={"px-4 py-3 cursor-pointer transition-colors ".concat(index === highlightedIndex
                    ? 'bg-[#0090ea] text-white'
                    : 'text-white hover:bg-gray-700')} onClick={function () { return handleSelectUser(user); }}>
              <div className="font-medium" style={{ fontFamily: 'PingFang SC' }}>
                {user.name}
              </div>
              <div className="text-sm text-gray-400" style={{ fontFamily: 'PingFang SC' }}>
                ID: {user.id}
              </div>
            </div>); })}
        </div>)}

      {/* 无结果提示 */}
      {isOpen && !loading && inputValue.trim() && suggestions.length === 0 && (<div className="absolute z-50 w-full mt-1 bg-gray-800 border border-gray-600 rounded-[5px] shadow-lg">
          <div className="px-4 py-3 text-gray-400" style={{ fontFamily: 'PingFang SC' }}>
            未找到匹配的用户
          </div>
        </div>)}
    </div>);
};
exports.default = UserSearchAutocomplete;
