"use strict";
// 顶部导航组件
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var react_router_dom_1 = require("react-router-dom");
// 本地图标资源
var logo_svg_1 = require("../../assets/icons/logo.svg");
var Header = function () {
    var location = (0, react_router_dom_1.useLocation)();
    var navigate = (0, react_router_dom_1.useNavigate)();
    var navigationItems = [
        { path: '/dashboard', label: '性能演示', key: 'dashboard' },
        { path: '/explorer', label: '数据浏览器', key: 'explorer' },
        { path: '/user-query', label: '用户查询', key: 'userQuery' },
    ];
    var isActive = function (path) { return location.pathname === path; };
    return (<div className="relative w-full h-20 bg-[#3c3c3c]">
      {/* Logo 区域 */}
      <div className="absolute left-[96px] top-[15px] flex items-center">
        {/* Logo 图标组 - 使用合并的 Logo */}
        <div className="relative w-[45px] h-[50px]">
          <img alt="Logo" className="block max-w-none size-full" src={logo_svg_1.default}/>
        </div>

        {/* 分隔线 */}
        <div className="ml-5 flex items-center justify-center w-0 h-0">
              <div className=" left-0 right-0  h-[24px]  border-l-2 border-[#679cff]">
              
              </div>
        </div>

        {/* 标题文字 */}
        <div className="ml-5">
          <p className="text-[#ffffff] text-[18px] font-normal leading-normal" style={{ fontFamily: 'PingFang SC' }}>
            高性能计算引擎演示
          </p>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="absolute right-8 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
        {navigationItems.map(function (item) { return (<button key={item.key} onClick={function () { return navigate(item.path); }} className={"px-6 py-2 rounded-lg transition-colors ".concat(isActive(item.path)
                ? 'bg-primary-blue text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-700')} style={{ fontFamily: 'PingFang SC' }}>
            {item.label}
          </button>); })}
      </nav>
    </div>);
};
exports.default = Header;
