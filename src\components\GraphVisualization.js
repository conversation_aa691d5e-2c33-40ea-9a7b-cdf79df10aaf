"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var GraphVisualization = function (_a) {
    var zoomLevel = _a.zoomLevel, isActive = _a.isActive;
    var canvasRef = (0, react_1.useRef)(null);
    var _b = (0, react_1.useState)([]), nodes = _b[0], setNodes = _b[1];
    var _c = (0, react_1.useState)([]), edges = _c[0], setEdges = _c[1];
    var _d = (0, react_1.useState)(0), animationFrame = _d[0], setAnimationFrame = _d[1];
    // 初始化图谱数据
    (0, react_1.useEffect)(function () {
        var initialNodes = [
            { id: '1', x: 200, y: 150, radius: 20, color: '#1D77FF', label: '用户A' },
            { id: '2', x: 350, y: 100, radius: 15, color: '#FB956B', label: '用户B' },
            { id: '3', x: 300, y: 250, radius: 18, color: '#F2BC29', label: '用户C' },
            { id: '4', x: 150, y: 300, radius: 12, color: '#1D77FF', label: '用户D' },
            { id: '5', x: 400, y: 200, radius: 16, color: '#FB956B', label: '用户E' },
        ];
        var initialEdges = [
            { from: '1', to: '2' },
            { from: '1', to: '3' },
            { from: '2', to: '5' },
            { from: '3', to: '4' },
            { from: '4', to: '1' },
        ];
        setNodes(initialNodes);
        setEdges(initialEdges);
    }, []);
    // 绘制图谱
    var drawGraph = function () {
        var canvas = canvasRef.current;
        if (!canvas)
            return;
        var ctx = canvas.getContext('2d');
        if (!ctx)
            return;
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        // 应用缩放
        var scale = zoomLevel / 100;
        ctx.save();
        ctx.scale(scale, scale);
        // 绘制边
        ctx.strokeStyle = '#9D9D9D';
        ctx.lineWidth = 2;
        edges.forEach(function (edge) {
            var fromNode = nodes.find(function (n) { return n.id === edge.from; });
            var toNode = nodes.find(function (n) { return n.id === edge.to; });
            if (fromNode && toNode) {
                ctx.beginPath();
                ctx.moveTo(fromNode.x, fromNode.y);
                ctx.lineTo(toNode.x, toNode.y);
                ctx.stroke();
            }
        });
        // 绘制节点
        nodes.forEach(function (node) {
            // 绘制节点圆圈
            ctx.beginPath();
            ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);
            ctx.fillStyle = node.color;
            ctx.fill();
            ctx.strokeStyle = '#FFFFFF';
            ctx.lineWidth = 2;
            ctx.stroke();
            // 绘制标签
            ctx.fillStyle = '#FFFFFF';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(node.label, node.x, node.y + node.radius + 15);
        });
        ctx.restore();
    };
    // 动画循环
    (0, react_1.useEffect)(function () {
        if (!isActive)
            return;
        var animate = function () {
            // 简单的节点动画 - 轻微的浮动效果
            setNodes(function (prevNodes) {
                return prevNodes.map(function (node) { return (__assign(__assign({}, node), { y: node.y + Math.sin(animationFrame * 0.02 + parseInt(node.id)) * 0.5 })); });
            });
            drawGraph();
            setAnimationFrame(function (prev) { return prev + 1; });
            requestAnimationFrame(animate);
        };
        var animationId = requestAnimationFrame(animate);
        return function () { return cancelAnimationFrame(animationId); };
    }, [isActive, nodes, edges, zoomLevel, animationFrame]);
    // 初始绘制
    (0, react_1.useEffect)(function () {
        drawGraph();
    }, [nodes, edges, zoomLevel]);
    return (<div className="w-full h-full flex items-center justify-center bg-gray-900 rounded-lg">
      <canvas ref={canvasRef} width={600} height={400} className="border border-gray-700 rounded" style={{
            maxWidth: '100%',
            maxHeight: '100%',
            background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
        }}/>
    </div>);
};
exports.default = GraphVisualization;
