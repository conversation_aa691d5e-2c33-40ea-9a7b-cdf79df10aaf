"use strict";
// 图服务模块的统一导出
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphService = void 0;
var thrift = require("thrift");
// @ts-ignore - Generated thrift file doesn't have proper types
var GraphCalculator_mjs_1 = require("../../../rpc/gen-nodejs/GraphCalculator.mjs");
console.log('Graph service imports successful');
var host = 'localhost';
var port = 8080;
var options = {
    protocol: thrift.TJSONProtocol,
    path: '/graphcalculator',
    headers: {
        'Content-Type': 'application/vnd.apache.thrift.json',
    },
    useCORS: true,
};
var connectionError = null;
// Create a Thrift connection and client
function createThriftClient() {
    var connection = thrift.createXHRConnection(host, port, options);
    var client = thrift.createXHRClient(GraphCalculator_mjs_1.Client, connection);
    console.log('Graph client created');
    // Handle connection errors
    connection.on('error', function (error) {
        console.error('Thrift connection error:', error);
        connectionError = error;
    });
    return client;
}
var GraphService = /** @class */ (function () {
    function GraphService() {
        this.client = createThriftClient();
    }
    // Method to call showGraphs
    GraphService.prototype.getGraphs = function () {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, showGraphsPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("showGraphs request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error('showGraphs request timed out'));
                        }
                    }, 5000); // Set timeout to 5 seconds
                });
                showGraphsPromise = new Promise(function (resolve, reject) {
                    _this.client.showGraphs()
                        .then(resolve)
                        .catch(reject);
                });
                // Use Promise.race to handle timeout
                return [2 /*return*/, Promise.race([showGraphsPromise, timeout])];
            });
        });
    };
    // Method to ping the service
    GraphService.prototype.ping = function () {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, pingPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("ping request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error('ping request timed out'));
                        }
                    }, 5000);
                });
                pingPromise = new Promise(function (resolve, reject) {
                    _this.client.ping()
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([pingPromise, timeout])];
            });
        });
    };
    // Method to get vertex types
    GraphService.prototype.getVertexTypes = function (graphName) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, showVertexesPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("showVertexes request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error("showVertexes request timed out for graph: ".concat(graphName)));
                        }
                    }, 5000);
                });
                showVertexesPromise = new Promise(function (resolve, reject) {
                    _this.client.showVertexes(graphName)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([showVertexesPromise, timeout])];
            });
        });
    };
    // Method to get edge types
    GraphService.prototype.getEdgeTypes = function (graphName) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, showEdgesPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("showEdges request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error("showEdges request timed out for graph: ".concat(graphName)));
                        }
                    }, 5000);
                });
                showEdgesPromise = new Promise(function (resolve, reject) {
                    _this.client.showEdges(graphName)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([showEdgesPromise, timeout])];
            });
        });
    };
    // Method to get vertex count
    GraphService.prototype.getVertexCount = function (graphName, vertexType) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, getVertexCountPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("getVertexCount request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error("getVertexCount request timed out for graph: ".concat(graphName, ", type: ").concat(vertexType)));
                        }
                    }, 5000);
                });
                getVertexCountPromise = new Promise(function (resolve, reject) {
                    _this.client.getVertexCount(graphName, vertexType)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([getVertexCountPromise, timeout])];
            });
        });
    };
    // Method to get edge count
    GraphService.prototype.getEdgeCount = function (graphName, edgeType) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, getEdgeCountPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("getEdgeCount request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error("getEdgeCount request timed out for graph: ".concat(graphName, ", type: ").concat(edgeType)));
                        }
                    }, 5000);
                });
                getEdgeCountPromise = new Promise(function (resolve, reject) {
                    _this.client.getEdgeCount(graphName, edgeType)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([getEdgeCountPromise, timeout])];
            });
        });
    };
    // Method to scan vertices
    GraphService.prototype.scanVertex = function (graphName, vertexType, offset, count) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, scanVertexPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("scanVertex request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error("scanVertex request timed out for graph: ".concat(graphName, ", type: ").concat(vertexType)));
                        }
                    }, 5000);
                });
                scanVertexPromise = new Promise(function (resolve, reject) {
                    _this.client.scanVertex(graphName, vertexType, offset, count)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([scanVertexPromise, timeout])];
            });
        });
    };
    // Method to create new graph
    GraphService.prototype.createGraph = function (graphName) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, makeNewGraphPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("makeNewGraph request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error("makeNewGraph request timed out for graph: ".concat(graphName)));
                        }
                    }, 5000);
                });
                makeNewGraphPromise = new Promise(function (resolve, reject) {
                    _this.client.makeNewGraph(graphName)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([makeNewGraphPromise, timeout])];
            });
        });
    };
    // Method to insert vertex
    GraphService.prototype.insertVertex = function (graphName, vertexType, vertexJson) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, insertVertexPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("insertVertex request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error("insertVertex request timed out for graph: ".concat(graphName, ", type: ").concat(vertexType)));
                        }
                    }, 5000);
                });
                insertVertexPromise = new Promise(function (resolve, reject) {
                    _this.client.insertVertex(graphName, vertexType, vertexJson)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([insertVertexPromise, timeout])];
            });
        });
    };
    // Method to insert edge
    GraphService.prototype.insertEdge = function (graphName, edgeType, edgeJson) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, insertEdgePromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("insertEdge request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error("insertEdge request timed out for graph: ".concat(graphName, ", type: ").concat(edgeType)));
                        }
                    }, 5000);
                });
                insertEdgePromise = new Promise(function (resolve, reject) {
                    _this.client.insertEdge(graphName, edgeType, edgeJson)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([insertEdgePromise, timeout])];
            });
        });
    };
    // Method for filter query (用于复杂查询)
    GraphService.prototype.filterQuery = function (queryReq_1) {
        return __awaiter(this, arguments, void 0, function (queryReq, midFilterFlag) {
            var timeout, filterQueryPromise;
            var _this = this;
            if (midFilterFlag === void 0) { midFilterFlag = 0; }
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("filterQuery request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error('filterQuery request timed out'));
                        }
                    }, 10000); // 复杂查询使用更长的超时时间
                });
                filterQueryPromise = new Promise(function (resolve, reject) {
                    _this.client.filterQuery(queryReq, midFilterFlag)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([filterQueryPromise, timeout])];
            });
        });
    };
    // Method for regular query (常规查询)
    GraphService.prototype.regulerQuery = function (queryReq) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, regulerQueryPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("regulerQuery request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error('regulerQuery request timed out'));
                        }
                    }, 10000);
                });
                regulerQueryPromise = new Promise(function (resolve, reject) {
                    _this.client.regulerQuery(queryReq)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([regulerQueryPromise, timeout])];
            });
        });
    };
    // Method for recommend calculation (推荐计算)
    GraphService.prototype.recommendCalc = function (queryReq) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, recommendCalcPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("recommendCalc request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error('recommendCalc request timed out'));
                        }
                    }, 15000); // 推荐算法需要更长时间
                });
                recommendCalcPromise = new Promise(function (resolve, reject) {
                    _this.client.recommendCalc(queryReq)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([recommendCalcPromise, timeout])];
            });
        });
    };
    // Method for similar mutual calculation (相似度计算)
    GraphService.prototype.similarMutualCalc = function (queryReq) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, similarMutualCalcPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("similarMutualCalc request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error('similarMutualCalc request timed out'));
                        }
                    }, 15000);
                });
                similarMutualCalcPromise = new Promise(function (resolve, reject) {
                    _this.client.similarMutualCalc(queryReq)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([similarMutualCalcPromise, timeout])];
            });
        });
    };
    // Method for path finding calculation (路径查找)
    GraphService.prototype.pathFindCalc = function (queryReq) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, pathFindCalcPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("pathFindCalc request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error('pathFindCalc request timed out'));
                        }
                    }, 15000);
                });
                pathFindCalcPromise = new Promise(function (resolve, reject) {
                    _this.client.pathFindCalc(queryReq)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([pathFindCalcPromise, timeout])];
            });
        });
    };
    // Method to get vertex info by ID
    GraphService.prototype.getVertexInfoById = function (graphName, vertexType, vertexId) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, getVertexInfoPromise;
            var _this = this;
            return __generator(this, function (_a) {
                timeout = new Promise(function (_, reject) {
                    setTimeout(function () {
                        if (connectionError) {
                            reject(new Error("getVertexInfoById request timed out. Connection error: ".concat(connectionError.message)));
                        }
                        else {
                            reject(new Error("getVertexInfoById request timed out for graph: ".concat(graphName, ", vertex: ").concat(vertexId)));
                        }
                    }, 5000);
                });
                getVertexInfoPromise = new Promise(function (resolve, reject) {
                    _this.client.getVertexInfoById(graphName, vertexType, vertexId)
                        .then(resolve)
                        .catch(reject);
                });
                return [2 /*return*/, Promise.race([getVertexInfoPromise, timeout])];
            });
        });
    };
    // Method for simple page query (简单分页查询)
    GraphService.prototype.simplePageQuery = function (queryReq) {
        return __awaiter(this, void 0, void 0, function () {
            var timeout, simplePageQueryPromise, rawResult, processedResult, error_1;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log('GraphService.simplePageQuery 开始执行，请求参数:', queryReq);
                        timeout = new Promise(function (_, reject) {
                            setTimeout(function () {
                                if (connectionError) {
                                    reject(new Error("simplePageQuery request timed out. Connection error: ".concat(connectionError.message)));
                                }
                                else {
                                    reject(new Error('simplePageQuery request timed out'));
                                }
                            }, 15000); // 增加超时时间到15秒
                        });
                        simplePageQueryPromise = new Promise(function (resolve, reject) {
                            console.log('调用 this.client.simplePageQuery...');
                            // 尝试捕获更底层的错误
                            try {
                                _this.client.simplePageQuery(queryReq)
                                    .then(function (result) {
                                    resolve(result);
                                })
                                    .catch(function (error) {
                                    // 如果是 JSON 解析错误，尝试获取原始响应
                                    if (error.message && error.message.includes('Unexpected')) {
                                        console.error('这是一个 JSON 解析错误，可能是服务器返回了非 JSON 格式的数据');
                                    }
                                    reject(error);
                                });
                            }
                            catch (syncError) {
                                console.error('同步调用错误:', syncError);
                                reject(syncError);
                            }
                        });
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, Promise.race([simplePageQueryPromise, timeout])];
                    case 2:
                        rawResult = _a.sent();
                        console.log('simplePageQuery 原始结果:', rawResult);
                        processedResult = this.processPageListResult(rawResult);
                        console.log('simplePageQuery 处理后结果:', processedResult);
                        return [2 /*return*/, processedResult];
                    case 3:
                        error_1 = _a.sent();
                        console.error('simplePageQuery 执行失败:', error_1);
                        throw error_1;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    // 处理 PageList_Res 结果，将 Buffer 格式的 ID 转换为数字
    GraphService.prototype.processPageListResult = function (rawResult) {
        if (!rawResult || !rawResult.ids || !Array.isArray(rawResult.ids)) {
            return rawResult;
        }
        var processedIds = [];
        for (var _i = 0, _a = rawResult.ids; _i < _a.length; _i++) {
            var idObj = _a[_i];
            var actualId = void 0;
            if (typeof idObj === 'number') {
                // 如果是直接的数字
                actualId = idObj;
            }
            else if (idObj && typeof idObj === 'object' && idObj.buffer) {
                // 如果是 Buffer 对象，需要转换为数字
                try {
                    var buffer = idObj.buffer;
                    console.log('Buffer 对象详情:', idObj);
                    console.log('Buffer 类型:', buffer.constructor.name);
                    console.log('Buffer 内容:', buffer);
                    if (buffer instanceof Uint8Array) {
                        // 处理 Uint8Array 格式的 Buffer（64 位整数的大端序）
                        var value = 0;
                        for (var i = 0; i < buffer.length; i++) {
                            value = value * 256 + buffer[i];
                        }
                        actualId = value;
                        console.log("Uint8Array [".concat(Array.from(buffer).join(','), "] \u8F6C\u6362\u4E3A ID: ").concat(actualId));
                    }
                    else if (buffer.type === 'Buffer' && Array.isArray(buffer.data)) {
                        // 处理传统的 Buffer 格式
                        var value = 0;
                        for (var i = 0; i < buffer.data.length; i++) {
                            value = value * 256 + buffer.data[i];
                        }
                        actualId = value;
                        console.log("Buffer ".concat(JSON.stringify(buffer.data), " \u8F6C\u6362\u4E3A ID: ").concat(actualId));
                    }
                    else {
                        console.warn('未知的 Buffer 格式:', idObj);
                        console.warn('Buffer 详情:', buffer);
                        continue;
                    }
                }
                catch (error) {
                    console.error('转换 Buffer ID 失败:', error, idObj);
                    continue;
                }
            }
            else {
                console.warn('未知的 ID 格式:', idObj);
                continue;
            }
            processedIds.push(actualId);
        }
        return __assign(__assign({}, rawResult), { ids: processedIds });
    };
    return GraphService;
}());
exports.GraphService = GraphService;
// Create and export the default instance
var graphService = new GraphService();
exports.default = graphService;
