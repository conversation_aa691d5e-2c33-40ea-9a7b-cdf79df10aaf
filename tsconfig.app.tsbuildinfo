{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/graphdataexplorer.tsx", "./src/components/graphvisualization.tsx", "./src/components/icons.tsx", "./src/components/infosidebar.tsx", "./src/components/infotooltip.tsx", "./src/components/searchicon.tsx", "./src/components/sigmagraphvisualization.tsx", "./src/components/toast.tsx", "./src/components/toastcontainer.tsx", "./src/components/toastdemo.tsx", "./src/components/userquerydemo.tsx", "./src/components/usersearchautocomplete.tsx", "./src/components/layout/header.tsx", "./src/components/layout/layout.tsx", "./src/components/layout/index.ts", "./src/components/views/dashboardview.tsx", "./src/components/views/explorerview.tsx", "./src/components/views/userqueryview.tsx", "./src/components/views/index.ts", "./src/contexts/toastcontext.tsx", "./src/hooks/usefullscreen.ts", "./src/hooks/usegraphquery.ts", "./src/hooks/usetoast.ts", "./src/router/index.tsx", "./src/services/graph/userqueryservice.ts", "./src/services/graph/index.ts", "./src/services/graph/types.ts"], "errors": true, "version": "5.8.3"}