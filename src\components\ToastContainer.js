"use strict";
// Toast 容器组件
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var Toast_1 = require("./Toast");
var ToastContainer = function (_a) {
    var toasts = _a.toasts, onRemoveToast = _a.onRemoveToast;
    return (<div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map(function (toast, index) { return (<div key={toast.id} style={{
                transform: "translateY(".concat(index * 10, "px)"),
                zIndex: 1000 - index
            }}>
          <Toast_1.default message={toast.message} type={toast.type} duration={toast.duration} onClose={function () { return onRemoveToast(toast.id); }}/>
        </div>); })}
    </div>);
};
exports.default = ToastContainer;
