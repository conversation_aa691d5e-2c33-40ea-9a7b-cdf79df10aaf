"use strict";
// 主布局组件
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var Header_1 = require("./Header");
var Layout = function (_a) {
    var children = _a.children;
    return (<div className="h-screen min-w-[1200px] min-h-[900px] overflow-hidden bg-primary-dark">
      <Header_1.default />
      <main className="px-24 py-4">
        {children}
      </main>
    </div>);
};
exports.default = Layout;
