"use strict";
// 信息浮窗组件
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var InfoTooltip = function (_a) {
    var x = _a.x, y = _a.y, visible = _a.visible, title = _a.title, content = _a.content, type = _a.type;
    if (!visible)
        return null;
    var getTypeColor = function () {
        return type === 'node' ? '#1D77FF' : '#FB956B';
    };
    var getTypeIcon = function () {
        return type === 'node' ? '●' : '—';
    };
    return (<div className="absolute z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-lg p-3 max-w-xs pointer-events-none" style={{
            left: x + 10,
            top: y - 10,
            fontFamily: 'PingFang SC',
        }}>
      {/* 标题 */}
      <div className="flex items-center gap-2 mb-2">
        <span className="text-lg font-bold" style={{ color: getTypeColor() }}>
          {getTypeIcon()}
        </span>
        <span className="text-white font-semibold text-sm">
          {title}
        </span>
        <span className="text-gray-400 text-xs">
          ({type === 'node' ? '节点' : '关系'})
        </span>
      </div>

      {/* 内容 */}
      <div className="space-y-1">
        {Object.entries(content).map(function (_a) {
            var key = _a[0], value = _a[1];
            return (<div key={key} className="flex justify-between text-xs">
            <span className="text-gray-300">{key}:</span>
            <span className="text-white ml-2 truncate">
              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
            </span>
          </div>);
        })}
      </div>

      {/* 小箭头 */}
      <div className="absolute w-2 h-2 bg-gray-800 border-l border-b border-gray-600 transform rotate-45" style={{
            left: -4,
            top: 16,
        }}/>
    </div>);
};
exports.default = InfoTooltip;
