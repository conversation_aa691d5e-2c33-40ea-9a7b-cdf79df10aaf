"use strict";
// 用户查询视图组件
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var UserQueryDemo_1 = require("../UserQueryDemo");
var UserQueryView = function () {
    return (<div className="space-y-8">
      {/* 页面标题 */}
      <div className="bg-card-bg rounded-[20px] p-12">
        <h1 className="text-primary-blue text-spec-header font-semibold mb-4 font-arial">
          用户查询演示
        </h1>
        <p className="text-text-primary text-spec-body leading-normal font-arial">
          演示如何使用 Thrift 接口进行复杂的用户查询操作，包括用户名查询、最大深度路径查询和相似用户推荐。
          支持配置不同的图数据库参数，并提供实时的查询结果展示。
        </p>
      </div>

      {/* 用户查询演示组件 */}
      <UserQueryDemo_1.default />
    </div>);
};
exports.default = UserQueryView;
