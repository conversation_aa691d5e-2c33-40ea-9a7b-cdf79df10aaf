"use strict";
// 图查询 Hook
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useGraphQuery = void 0;
var react_1 = require("react");
var neo4j_query_table_data_2025_7_30_json_1 = require("../assets/neo4j_query_table_data_2025-7-30.json");
var useGraphQuery = function (onQueryComplete) {
    // 双数据源状态
    var _a = (0, react_1.useState)(null), overviewData = _a[0], setOverviewData = _a[1];
    var _b = (0, react_1.useState)(null), queryData = _b[0], setQueryData = _b[1];
    var _c = (0, react_1.useState)('overview'), viewMode = _c[0], setViewMode = _c[1];
    var _d = (0, react_1.useState)(false), loading = _d[0], setLoading = _d[1];
    var _e = (0, react_1.useState)(null), error = _e[0], setError = _e[1];
    // 当前显示的数据
    var currentData = viewMode === 'query' && queryData ? queryData : overviewData;
    // 使用 useRef 来避免依赖问题
    var onQueryCompleteRef = (0, react_1.useRef)(onQueryComplete);
    onQueryCompleteRef.current = onQueryComplete;
    var executeQuery = (0, react_1.useCallback)(function (username_1, expandDepth_1) {
        var args_1 = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            args_1[_i - 2] = arguments[_i];
        }
        return __awaiter(void 0, __spreadArray([username_1, expandDepth_1], args_1, true), void 0, function (username, expandDepth, recommendSimilar, userId) {
            var startTime, result, endTime, duration, formattedDuration, errorMessage;
            if (recommendSimilar === void 0) { recommendSimilar = false; }
            return __generator(this, function (_a) {
                if (!username.trim()) {
                    setError('请输入用户名');
                    return [2 /*return*/];
                }
                if (expandDepth < 1 || expandDepth > 10) {
                    setError('扩大深度必须在1-10之间');
                    return [2 /*return*/];
                }
                setLoading(true);
                setError(null);
                startTime = performance.now();
                try {
                    console.log('开始执行查询:', { username: username, expandDepth: expandDepth, recommendSimilar: recommendSimilar, userId: userId });
                    // 暂时直接使用假数据展示功能
                    console.log('使用假数据展示图计算查询功能');
                    result = generateRealisticMockData(username, userId || 0, expandDepth, recommendSimilar);
                    console.log('生成的查询数据:', result);
                    setQueryData(result);
                    setViewMode('query'); // 查询完成后切换到查询视图
                    console.log('查询数据已设置，节点数:', result.nodes.length, '边数:', result.edges.length);
                    endTime = performance.now();
                    duration = (endTime - startTime) / 1000;
                    formattedDuration = parseFloat(duration.toFixed(2));
                    console.log("\u67E5\u8BE2\u5B8C\u6210\uFF0C\u8017\u65F6: ".concat(formattedDuration, " \u79D2"));
                    // 调用完成回调
                    if (onQueryCompleteRef.current) {
                        onQueryCompleteRef.current(formattedDuration);
                    }
                }
                catch (err) {
                    errorMessage = err instanceof Error ? err.message : '查询失败';
                    console.error('查询错误:', err);
                    setError(errorMessage);
                }
                finally {
                    setLoading(false);
                }
                return [2 /*return*/];
            });
        });
    }, []);
    // 初始化全貌数据
    var initializeOverview = (0, react_1.useCallback)(function () {
        console.log('初始化全貌数据');
        var overviewResult = generateOverviewMockData();
        setOverviewData(overviewResult);
        setViewMode('overview');
        console.log('全貌数据已设置，节点数:', overviewResult.nodes.length, '边数:', overviewResult.edges.length);
    }, []);
    // 切换到全貌视图
    var switchToOverview = (0, react_1.useCallback)(function () {
        console.log('切换到全貌视图');
        setViewMode('overview');
        setError(null);
    }, []);
    // 清除查询数据（相当于取消）
    var clearData = (0, react_1.useCallback)(function () {
        console.log('清除查询数据，回到全貌');
        setQueryData(null);
        setViewMode('overview');
        setError(null);
    }, []);
    var clearError = (0, react_1.useCallback)(function () {
        setError(null);
    }, []);
    // 组件挂载时初始化全貌数据
    (0, react_1.useEffect)(function () {
        if (!overviewData) {
            initializeOverview();
        }
    }, [overviewData, initializeOverview]);
    return {
        data: currentData,
        overviewData: overviewData,
        queryData: queryData,
        viewMode: viewMode,
        loading: loading,
        error: error,
        executeQuery: executeQuery,
        clearData: clearData,
        clearError: clearError,
        switchToOverview: switchToOverview,
        initializeOverview: initializeOverview,
    };
};
exports.useGraphQuery = useGraphQuery;
// 生成多跳查询的模拟数据 - 支持EgoNode和Friend类型
function generateRealisticMockData(username, userId, expandDepth, recommendSimilar) {
    var nodes = [];
    var edges = [];
    // 添加主用户节点（EgoNode）
    var centerNode = {
        id: "ego_".concat(userId),
        label: "EgoNode_".concat(userId),
        x: 0,
        y: 0,
        size: 25,
        color: '#3498db',
        type: 'ego',
        features: ["@User".concat(userId), "@Profile".concat(userId), "@Data".concat(userId)],
    };
    nodes.push(centerNode);
    var nodeIdCounter = userId + 1;
    var allNodeIds = [centerNode.id];
    // 根据扩展深度生成连接的用户
    for (var depth = 1; depth <= expandDepth; depth++) {
        var nodeCount = Math.max(3, Math.min(8, 12 - depth * 2)); // 每层3-8个节点
        var currentLayerNodes = [];
        for (var i = 0; i < nodeCount; i++) {
            var nodeId = "friend_".concat(nodeIdCounter++);
            var angle = (i / nodeCount) * 2 * Math.PI + (Math.random() - 0.5) * 0.5;
            var radius = depth * (80 + Math.random() * 40);
            var friendNode = {
                id: nodeId,
                label: "Friend_".concat(nodeIdCounter - 1),
                x: Math.cos(angle) * radius,
                y: Math.sin(angle) * radius,
                size: Math.max(10, 22 - depth * 2),
                color: '#e74c3c',
                type: 'friend',
                features: ["@Friend".concat(nodeIdCounter - 1), "@Network".concat(nodeIdCounter - 1), "@Hop".concat(depth)],
            };
            nodes.push(friendNode);
            currentLayerNodes.push(nodeId);
            allNodeIds.push(nodeId);
            // 连接到上一层的节点
            if (depth === 1) {
                // 第一层直接连接到主用户（EgoNode）
                edges.push({
                    id: "edge_".concat(centerNode.id, "_").concat(nodeId),
                    source: centerNode.id,
                    target: nodeId,
                    weight: 1,
                    color: '#2980b9',
                    type: 'HAS_FRIEND',
                });
            }
            else {
                // 后续层连接到前一层的随机节点
                var prevLayerStart = allNodeIds.length - currentLayerNodes.length - nodeCount;
                var prevLayerEnd = allNodeIds.length - currentLayerNodes.length;
                var sourceNodeId = allNodeIds[prevLayerStart + Math.floor(Math.random() * (prevLayerEnd - prevLayerStart))];
                edges.push({
                    id: "edge_".concat(sourceNodeId, "_").concat(nodeId),
                    source: sourceNodeId,
                    target: nodeId,
                    weight: 0.8,
                    color: '#95a5a6',
                    type: 'FRIEND_OF',
                });
            }
        }
        // 在同一层的节点之间添加一些连接
        if (currentLayerNodes.length > 2) {
            var connectionsCount = Math.floor(currentLayerNodes.length / 3);
            for (var i = 0; i < connectionsCount; i++) {
                var node1 = currentLayerNodes[Math.floor(Math.random() * currentLayerNodes.length)];
                var node2 = currentLayerNodes[Math.floor(Math.random() * currentLayerNodes.length)];
                if (node1 !== node2) {
                    edges.push({
                        id: "edge_".concat(node1, "_").concat(node2),
                        source: node1,
                        target: node2,
                        weight: 0.7,
                        color: '#95a5a6',
                        type: 'FRIEND_OF',
                    });
                }
            }
        }
    }
    // 如果启用推荐，添加相似的EgoNode
    if (recommendSimilar) {
        var recommendedEgoId = userId + 1000;
        var recommendedEgoNode = {
            id: "ego_".concat(recommendedEgoId),
            label: "EgoNode_".concat(recommendedEgoId, "_\u63A8\u8350"),
            x: 300,
            y: 0,
            size: 22,
            color: '#9b59b6', // 紫色表示推荐
            type: 'ego',
            features: ["@User".concat(recommendedEgoId), "@Similar".concat(recommendedEgoId), "@Recommended"],
        };
        nodes.push(recommendedEgoNode);
        // 推荐EgoNode的朋友（2-3个）
        var recommendedFriendCount = Math.floor(Math.random() * 2) + 2;
        for (var i = 0; i < recommendedFriendCount; i++) {
            var recFriendId = "friend_".concat(recommendedEgoId, "_").concat(i + 1);
            var angle = (i / recommendedFriendCount) * 2 * Math.PI;
            var radius = 60;
            var recFriendNode = {
                id: recFriendId,
                label: "Friend_".concat(recommendedEgoId, "_").concat(i + 1),
                x: 300 + Math.cos(angle) * radius,
                y: Math.sin(angle) * radius,
                size: 12,
                color: '#e74c3c',
                type: 'friend',
                features: ["@Friend".concat(recommendedEgoId, "_").concat(i + 1), "@RecNetwork", "@Hop1"],
            };
            nodes.push(recFriendNode);
            // 连接推荐EgoNode和其朋友
            edges.push({
                id: "edge_".concat(recommendedEgoNode.id, "_").concat(recFriendId),
                source: recommendedEgoNode.id,
                target: recFriendId,
                weight: 1,
                color: '#2980b9',
                type: 'HAS_FRIEND',
            });
        }
    }
    // 计算真实的统计信息
    var connectionCounts = new Map();
    edges.forEach(function (edge) {
        connectionCounts.set(edge.source, (connectionCounts.get(edge.source) || 0) + 1);
        connectionCounts.set(edge.target, (connectionCounts.get(edge.target) || 0) + 1);
    });
    var connections = Array.from(connectionCounts.values());
    var maxConnections = connections.length > 0 ? Math.max.apply(Math, connections) : 0;
    var avgConnections = connections.length > 0 ? Math.round((connections.reduce(function (a, b) { return a + b; }, 0) / connections.length) * 10) / 10 : 0;
    var statistics = {
        totalNodes: nodes.length,
        totalEdges: edges.length,
        maxConnections: maxConnections,
        avgConnections: avgConnections,
        recommendedUsers: recommendSimilar ? nodes.filter(function (n) { return n.label.includes('推荐'); }).length : 0,
    };
    return {
        nodes: nodes,
        edges: edges,
        statistics: statistics,
    };
}
// 生成全貌展示的模拟数据
// 生成全貌展示的模拟数据 - 基于真实 Neo4j 数据
function generateOverviewMockData() {
    // 使用导入的 Neo4j 数据
    var data = neo4j_query_table_data_2025_7_30_json_1.default;
    // 转换为 Sigma 格式
    var sigmaData = convertToSigmaFormat(data);
    // 转换为我们的 GraphNode 和 GraphEdge 格式
    var nodes = sigmaData.nodes.map(function (node) { return ({
        id: node.key,
        label: node.label,
        x: node.x,
        y: node.y,
        size: node.size,
        color: node.color,
        type: node.tag,
        features: node.features, // 传递 features 属性
    }); });
    var edges = sigmaData.edges.map(function (edge) { return ({
        id: edge.key,
        source: edge.source,
        target: edge.target,
        weight: edge.size || 1,
        color: edge.color,
    }); });
    // 计算统计信息
    var connectionCounts = new Map();
    edges.forEach(function (edge) {
        connectionCounts.set(edge.source, (connectionCounts.get(edge.source) || 0) + 1);
        connectionCounts.set(edge.target, (connectionCounts.get(edge.target) || 0) + 1);
    });
    var connections = Array.from(connectionCounts.values());
    var maxConnections = connections.length > 0 ? Math.max.apply(Math, connections) : 0;
    var avgConnections = connections.length > 0 ? Math.round((connections.reduce(function (a, b) { return a + b; }, 0) / connections.length) * 10) / 10 : 0;
    var statistics = {
        totalNodes: nodes.length,
        totalEdges: edges.length,
        maxConnections: maxConnections,
        avgConnections: avgConnections,
        recommendedUsers: 0,
    };
    return {
        nodes: nodes,
        edges: edges,
        statistics: statistics,
    };
}
// 根据节点类型获取大小
function getNodeSize(nodeType) {
    switch (nodeType) {
        case 'ego':
            return 20; // 中心用户 - 最大
        case 'Circle':
            return 15; // 聚合分类 - 中等
        case 'friend':
            return 8; // 朋友节点 - 最小
        default:
            return 10; // 默认大小
    }
}
// 根据节点类型获取颜色
function getNodeColor(nodeType) {
    switch (nodeType) {
        case 'ego':
            return '#3498db'; // 蓝色 - 中心用户
        case 'circle':
            return '#f39c12'; // 橙色 - 聚合分类
        case 'friend':
            return '#e74c3c'; // 红色 - 朋友节点
        default:
            return '#95a5a6'; // 灰色 - 默认
    }
}
// Neo4j 数据转换为 Sigma 格式的函数
function convertToSigmaFormat(neo4jData) {
    var sigmaData = {
        nodes: [],
        edges: []
    };
    // 处理所有记录
    neo4jData.forEach(function (record, recordIndex) {
        var graphData = record.graph_data;
        // 转换节点
        graphData.nodes.forEach(function (node) {
            if (!sigmaData.nodes.find(function (n) { return n.key === String(node.id); })) {
                sigmaData.nodes.push({
                    key: String(node.id),
                    label: node.label || String(node.id),
                    tag: node.type,
                    dataset: node.dataset,
                    features: node.features || [],
                    // Sigma.js 样式属性 - 支持三种节点类型
                    x: Math.random() * 1000,
                    y: Math.random() * 1000,
                    size: getNodeSize(node.type),
                    color: getNodeColor(node.type)
                });
            }
        });
        // 转换边 - 检查重复边
        graphData.edges.forEach(function (edge, edgeIndex) {
            if (edge.source && edge.target) {
                var sourceStr_1 = String(edge.source);
                var targetStr_1 = String(edge.target);
                // 检查是否已存在相同的边（双向检查）
                var edgeExists = sigmaData.edges.some(function (existingEdge) {
                    return (existingEdge.source === sourceStr_1 && existingEdge.target === targetStr_1) ||
                        (existingEdge.source === targetStr_1 && existingEdge.target === sourceStr_1);
                });
                if (!edgeExists) {
                    sigmaData.edges.push({
                        key: "edge_".concat(recordIndex, "_").concat(edgeIndex),
                        source: sourceStr_1,
                        target: targetStr_1,
                        label: edge.type,
                        type: edge.type,
                        dataset: edge.dataset,
                        // Sigma.js 样式属性
                        color: edge.type === 'HAS_FRIEND' ? '#2980b9' : '#95a5a6',
                        size: edge.type === 'HAS_FRIEND' ? 3 : 1
                    });
                }
                else {
                    console.log("\u8DF3\u8FC7\u91CD\u590D\u8FB9: ".concat(sourceStr_1, " -> ").concat(targetStr_1));
                }
            }
        });
    });
    return sigmaData;
}
