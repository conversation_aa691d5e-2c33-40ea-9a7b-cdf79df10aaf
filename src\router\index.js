"use strict";
// 路由配置
Object.defineProperty(exports, "__esModule", { value: true });
exports.router = void 0;
var react_router_dom_1 = require("react-router-dom");
var Layout_1 = require("../components/layout/Layout");
var views_1 = require("../components/views");
exports.router = (0, react_router_dom_1.createBrowserRouter)([
    {
        path: '/',
        element: <Layout_1.default><react_router_dom_1.Navigate to="/dashboard" replace/></Layout_1.default>,
    },
    {
        path: '/dashboard',
        element: (<Layout_1.default>
        <views_1.DashboardView />
      </Layout_1.default>),
    },
    {
        path: '/explorer',
        element: (<Layout_1.default>
        <views_1.ExplorerView />
      </Layout_1.default>),
    },
    {
        path: '/user-query',
        element: (<Layout_1.default>
        <views_1.UserQueryView />
      </Layout_1.default>),
    },
    {
        path: '*',
        element: <Layout_1.default><react_router_dom_1.Navigate to="/dashboard" replace/></Layout_1.default>,
    },
]);
exports.default = exports.router;
