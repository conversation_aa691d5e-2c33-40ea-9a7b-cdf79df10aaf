"use strict";
// 性能演示视图组件 - 严格按照 Figma 设计稿
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var useGraphQuery_1 = require("../../hooks/useGraphQuery");
var SigmaGraphVisualization_1 = require("../SigmaGraphVisualization");
var UserSearchAutocomplete_1 = require("../UserSearchAutocomplete");
var ToastContext_1 = require("../../contexts/ToastContext");
var data_display_png_1 = require("../../assets/icons/data-display.png");
var people_search_one_svg_1 = require("../../assets/icons/people-search-one.svg");
var performance_icon_svg_1 = require("../../assets/icons/performance-icon.svg");
var polygon_svg_1 = require("../../assets/icons/polygon.svg");
var ellipse_svg_1 = require("../../assets/icons/ellipse.svg");
// 图片资源常量（来自 Figma）
var DashboardView = function () {
    var _a = (0, react_1.useState)(null), selectedUser = _a[0], setSelectedUser = _a[1];
    var _b = (0, react_1.useState)(""), expandDepth = _b[0], setExpandDepth = _b[1];
    var _c = (0, react_1.useState)(false), recommendSimilar = _c[0], setRecommendSimilar = _c[1];
    // Toast 功能
    var showSuccess = (0, ToastContext_1.useToast)().showSuccess;
    // 查询完成回调 - 使用 useRef 来避免依赖问题
    var showSuccessRef = (0, react_1.useRef)(showSuccess);
    showSuccessRef.current = showSuccess;
    var handleQueryComplete = (0, react_1.useCallback)(function (duration) {
        showSuccessRef.current("\u8BA1\u7B97\u5B8C\u6210\uFF01\u8017\u65F6 ".concat(duration, " \u79D2"));
    }, []);
    // 使用图查询 Hook
    var _d = (0, useGraphQuery_1.useGraphQuery)(handleQueryComplete), data = _d.data, loading = _d.loading, error = _d.error, executeQuery = _d.executeQuery, clearData = _d.clearData, clearError = _d.clearError;
    console.log("查询结果:", data);
    // 处理查询提交
    var handleStartQuery = function () { return __awaiter(void 0, void 0, void 0, function () {
        var depth;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!selectedUser) {
                        alert("请先选择一个用户");
                        return [2 /*return*/];
                    }
                    depth = parseInt(expandDepth) || 2;
                    return [4 /*yield*/, executeQuery(selectedUser.name, depth, recommendSimilar, selectedUser.id)];
                case 1:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); };
    // 处理取消操作
    var handleCancel = function () {
        clearData();
        setSelectedUser(null);
        setExpandDepth("");
        setRecommendSimilar(false);
    };
    // 正常模式的渲染
    return (<div className="min-h-screen space-y-4">
      {/* 引言模块 - 按 Figma 尺寸：h-[117px] */}
      <div className="bg-[rgba(60,60,60,0.8)] rounded-[20px] px-8 py-4 h-[117px] flex flex-col justify-center">
        <h2 className="font-semibold text-[22px] text-[#679cff] mb-2" style={{ fontFamily: "PingFang SC" }}>
          计算引擎性能可视化演示
        </h2>
        <p className="text-[16px] text-white leading-normal" style={{ fontFamily: "PingFang SC" }}>
          本演示展现图计算引擎处理大规模复杂关系网络的高效性能。输入用户名后，系统会快速计算并呈现该用户的社交关系网络，同时推荐相似用户，计算过程与性能指标也会同步显示。
        </p>
      </div>

      {/* 主要内容区域 */}
      <div className="flex gap-4">
        {/* 左侧控制面板 - 按 Figma 尺寸：w-[560px] */}
        <div className="w-[560px] space-y-4">
          {/* 用户查询模块 - 按 Figma 尺寸：h-[338px] */}
          <div className="bg-[rgba(60,60,60,0.8)] rounded-[20px] px-8 py-6 h-[308px]">
            {/* 标题和图标 */}
            <div className="flex items-center gap-4 mb-4">
              <img src={people_search_one_svg_1.default} alt="用户查询" className="w-10 h-10"/>
              <h3 className="font-semibold text-[22px] text-white" style={{ fontFamily: "PingFang SC" }}>
                数据查询
              </h3>
            </div>

            {/* 用户名搜索 - 标签左，自动完成组件右 */}
            <div className="flex items-center gap-4 mb-3">
              <label className="font-semibold text-[18px] text-white w-20" style={{ fontFamily: "PingFang SC" }}>
                名称
              </label>
              <UserSearchAutocomplete_1.default value={selectedUser} onChange={setSelectedUser} placeholder="请输入名称搜索" className="w-[320px]"/>
            </div>

            {/* 扩大深度输入 - 标签左，输入框右 */}
            <div className="flex items-center gap-4 mb-4">
              <label className="font-semibold text-[18px] text-white w-20" style={{ fontFamily: "PingFang SC" }}>
                扩大深度
              </label>
              <input type="text" value={expandDepth} onChange={function (e) { return setExpandDepth(e.target.value); }} placeholder="请输入1~10之间的数字" className="h-[38px] px-4 bg-transparent w-[320px] border-2 border-white rounded-[5px] text-white placeholder-[#d9d9d9] focus:outline-none" style={{ fontFamily: "PingFang SC" }}/>
            </div>

            {/* 推荐相似用户开关 */}
            <div className="flex items-center gap-4 mb-4">
              <div className="relative">
                <div className={"w-20 h-10 rounded-[22px] cursor-pointer transition-colors ".concat(recommendSimilar ? "bg-[#0090ea]" : "bg-[#555555]")} onClick={function () { return setRecommendSimilar(!recommendSimilar); }}>
                  <div className={"w-10 h-10 transition-transform ".concat(recommendSimilar ? "translate-x-10" : "translate-x-0")}>
                    <img src={ellipse_svg_1.default} alt="" className="w-full h-full"/>
                  </div>
                </div>
              </div>
              <span className="text-[16px] text-white" style={{ fontFamily: "PingFang SC" }}>
                推荐相似用户
              </span>
            </div>

            {/* 按钮组 */}
            <div className="flex gap-4">
              <button onClick={handleStartQuery} disabled={loading || !selectedUser} className="w-[120px] h-10 bg-[#0090ea] rounded-[10px] text-[16px] text-white hover:bg-[#0090ea]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" style={{ fontFamily: "PingFang SC" }}>
                {loading ? "查询中..." : "开始计算"}
              </button>

              <button onClick={handleCancel} className="w-[120px] h-10 border-2 border-[#d9d9d9] rounded-[10px] text-[16px] text-white hover:bg-white/10 transition-colors" style={{ fontFamily: "PingFang SC" }}>
                取消
              </button>

              <button className="w-[120px] h-10 border-2 border-[#d9d9d9] rounded-[10px] text-[16px] text-white hover:bg-white/10 transition-colors flex items-center justify-center gap-2" style={{ fontFamily: "PingFang SC" }}>
                更多
                <img src={polygon_svg_1.default} alt="" className="w-4 h-4"/>
              </button>
            </div>
          </div>

          {/* 性能指标模块 - 按 Figma 尺寸：h-[457px] */}
          <div className="bg-[rgba(60,60,60,0.8)] rounded-[20px] min-h-[400px] px-8 py-6 h-[calc(100vh-567px)]">
            {/* 标题和图标 */}
            <div className="flex items-center gap-4 mb-4">
              <img src={performance_icon_svg_1.default} alt="性能指标" className="w-10 h-10"/>
              <h3 className="font-semibold text-[22px] text-white" style={{ fontFamily: "PingFang SC" }}>
                性能指标
              </h3>
            </div>

            {/* 指标网格 */}
            <div className="grid grid-cols-2 gap-x-8 gap-y-4">
              {/* 总结点数 */}
              <div>
                <div className="font-semibold text-[18px] text-white mb-1" style={{ fontFamily: "PingFang SC" }}>
                  总节点数
                </div>
                <div className="font-semibold text-[36px] text-white" style={{ fontFamily: "PingFang SC" }}>
                  {(data === null || data === void 0 ? void 0 : data.statistics.totalNodes) || 0}
                </div>
              </div>

              {/* 总关系数 */}
              <div>
                <div className="font-semibold text-[18px] text-white mb-1" style={{ fontFamily: "PingFang SC" }}>
                  总关系数
                </div>
                <div className="font-semibold text-[36px] text-white" style={{ fontFamily: "PingFang SC" }}>
                  {(data === null || data === void 0 ? void 0 : data.statistics.totalEdges) || 0}
                </div>
              </div>

              {/* 最大连接数 */}
              <div>
                <div className="font-semibold text-[18px] text-white mb-1" style={{ fontFamily: "PingFang SC" }}>
                  最大连接数
                </div>
                <div className="font-semibold text-[36px] text-white" style={{ fontFamily: "PingFang SC" }}>
                  {(data === null || data === void 0 ? void 0 : data.statistics.maxConnections) || 0}
                </div>
              </div>

              {/* 平均连接数 */}
              <div>
                <div className="font-semibold text-[18px] text-white mb-1" style={{ fontFamily: "PingFang SC" }}>
                  平均连接数
                </div>
                <div className="font-semibold text-[36px] text-white" style={{ fontFamily: "PingFang SC" }}>
                  {(data === null || data === void 0 ? void 0 : data.statistics.avgConnections) || 0}
                </div>
              </div>

              {/* 推荐用户数 */}
              <div className="col-span-1">
                <div className="font-semibold text-[18px] text-white mb-1" style={{ fontFamily: "PingFang SC" }}>
                  推荐用户数
                </div>
                <div className="font-semibold text-[36px] text-white" style={{ fontFamily: "PingFang SC" }}>
                  {(data === null || data === void 0 ? void 0 : data.statistics.recommendedUsers) || 0}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧数据展示区域 - 按 Figma 尺寸：h-[811px] */}
        <div className="flex-1">
          <div id="container" className="bg-[rgba(60,60,60,0.8)] rounded-[20px] min-h-[500px] px-8 py-6 h-[calc(100vh-242px)] flex flex-col">
            {/* 标题和工具栏 */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-4">
                <img src={data_display_png_1.default} alt="数据展示" className="w-10 h-10"/>
                <h3 className="font-semibold text-[22px] text-white" style={{ fontFamily: "PingFang SC" }}>
                  数据展示
                </h3>
              </div>
            </div>

            {/* 分割线 */}
            <div className="w-full h-0 border-t border-gray-600 mb-4"/>

            {/* 图谱展示区 - 使用 SigmaJS */}
            <div className="flex-1">
              <SigmaGraphVisualization_1.default data={data} loading={loading}/>
            </div>

            {/* 错误提示 */}
            {error && (<div className="absolute top-20 left-4 right-4 bg-red-500/80 text-white px-4 py-2 rounded z-10">
                <div className="flex justify-between items-center">
                  <span>{error}</span>
                  <button onClick={clearError} className="ml-4 text-white hover:text-gray-200">
                    ✕
                  </button>
                </div>
              </div>)}
          </div>
        </div>
      </div>
    </div>);
};
exports.default = DashboardView;
