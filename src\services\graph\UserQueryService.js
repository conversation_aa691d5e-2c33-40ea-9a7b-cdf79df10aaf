"use strict";
// 用户查询服务 - 实现用户名查询、最大深度查询和相似用户推荐
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUserQueryService = exports.userQueryService = exports.UserQueryService = void 0;
var index_1 = require("./index");
var UserQueryService = /** @class */ (function () {
    function UserQueryService(graphName, userVertexType, relationEdgeType) {
        if (userVertexType === void 0) { userVertexType = 'User'; }
        if (relationEdgeType === void 0) { relationEdgeType = 'Relation'; }
        this.graphName = graphName;
        this.userVertexType = userVertexType;
        this.relationEdgeType = relationEdgeType;
    }
    /**
     * 根据用户名查询用户信息
     * @param username 用户名
     * @returns 用户信息列表
     */
    UserQueryService.prototype.queryUserByName = function (username) {
        return __awaiter(this, void 0, void 0, function () {
            var filterQuery, userIds, userInfos, _i, userIds_1, userId, userInfo, userData, error_1;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 6, , 7]);
                        filterQuery = {
                            graph: this.graphName,
                            startVertex: this.userVertexType,
                            startIds: [],
                            throughEdges: [],
                            targetEdgeNodeName: '',
                            isReturnEdge: 0,
                            count: 100,
                            stepLayerLimit: 0,
                            filterMap: (_a = {},
                                _a[this.userVertexType] = {
                                    filters: [{
                                            filterType: 0, // 等值过滤
                                            nodeName: this.userVertexType,
                                            attrName: 'name',
                                            upper: username,
                                            lower: username,
                                            targetFilter: null
                                        }],
                                    relationType: 0 // AND关系
                                },
                                _a),
                            targetFilter: null
                        };
                        return [4 /*yield*/, index_1.default.filterQuery(filterQuery, 0)];
                    case 1:
                        userIds = _b.sent();
                        if (!userIds || userIds.length === 0) {
                            return [2 /*return*/, []];
                        }
                        userInfos = [];
                        _i = 0, userIds_1 = userIds;
                        _b.label = 2;
                    case 2:
                        if (!(_i < userIds_1.length)) return [3 /*break*/, 5];
                        userId = userIds_1[_i];
                        return [4 /*yield*/, index_1.default.getVertexInfoById(this.graphName, this.userVertexType, userId)];
                    case 3:
                        userInfo = _b.sent();
                        if (userInfo) {
                            userData = JSON.parse(userInfo);
                            userInfos.push({
                                id: userId,
                                name: userData.name || username,
                                properties: userData
                            });
                        }
                        _b.label = 4;
                    case 4:
                        _i++;
                        return [3 /*break*/, 2];
                    case 5: return [2 /*return*/, userInfos];
                    case 6:
                        error_1 = _b.sent();
                        console.error('查询用户失败:', error_1);
                        throw new Error("\u67E5\u8BE2\u7528\u6237\u5931\u8D25: ".concat(error_1));
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 查询指定用户的最大深度路径
     * @param startUserId 起始用户ID
     * @param targetUserId 目标用户ID（可选，如果不指定则查询从起始用户出发的最大深度）
     * @param maxDepth 最大深度限制
     * @returns 路径结果
     */
    UserQueryService.prototype.queryMaxDepthPath = function (startUserId_1, targetUserId_1) {
        return __awaiter(this, arguments, void 0, function (startUserId, targetUserId, maxDepth) {
            var pathQuery, paths, broadQuery, result, paths, _i, result_1, userId, error_2;
            if (maxDepth === void 0) { maxDepth = 6; }
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 5, , 6]);
                        if (!targetUserId) return [3 /*break*/, 2];
                        pathQuery = {
                            graph: this.graphName,
                            startId: startUserId,
                            destId: targetUserId,
                            throughEdges: [this.relationEdgeType],
                            filterVector: [],
                            topn: 10
                        };
                        return [4 /*yield*/, index_1.default.pathFindCalc(pathQuery)];
                    case 1:
                        paths = _a.sent();
                        return [2 /*return*/, paths.map(function (path, index) { return ({
                                path: path,
                                depth: path.length - 1,
                                edges: [] // 边信息需要额外查询
                            }); })];
                    case 2:
                        broadQuery = {
                            graph: this.graphName,
                            startVertex: this.userVertexType,
                            startIds: [startUserId],
                            throughEdges: [this.relationEdgeType],
                            targetEdgeNodeName: this.userVertexType,
                            isReturnEdge: 1,
                            count: 1000,
                            stepLayerLimit: maxDepth,
                            filterMap: {},
                            targetFilter: null
                        };
                        return [4 /*yield*/, index_1.default.filterQuery(broadQuery, 0)];
                    case 3:
                        result = _a.sent();
                        paths = [];
                        if (result && result.length > 0) {
                            for (_i = 0, result_1 = result; _i < result_1.length; _i++) {
                                userId = result_1[_i];
                                paths.push({
                                    path: [startUserId, userId],
                                    depth: 1, // 简化处理，实际深度需要通过路径计算
                                    edges: []
                                });
                            }
                        }
                        return [2 /*return*/, paths];
                    case 4: return [3 /*break*/, 6];
                    case 5:
                        error_2 = _a.sent();
                        console.error('查询最大深度路径失败:', error_2);
                        throw new Error("\u67E5\u8BE2\u6700\u5927\u6DF1\u5EA6\u8DEF\u5F84\u5931\u8D25: ".concat(error_2));
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 相似用户推荐
     * @param userId 用户ID
     * @param topN 返回前N个相似用户
     * @param minCommonConnections 最少共同连接数
     * @returns 相似用户列表
     */
    UserQueryService.prototype.recommendSimilarUsers = function (userId_1) {
        return __awaiter(this, arguments, void 0, function (userId, topN, minCommonConnections) {
            var recommendQuery, recommendations, similarUsers, _i, recommendations_1, rec, userInfo, properties, error_3;
            if (topN === void 0) { topN = 10; }
            if (minCommonConnections === void 0) { minCommonConnections = 2; }
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 6, , 7]);
                        recommendQuery = {
                            graph: this.graphName,
                            startId: userId,
                            throughEdges: [this.relationEdgeType],
                            filterVector: [],
                            corelationFlag: 1, // 启用相关性计算
                            multiFlag: 1, // 多路径计算
                            minCount: minCommonConnections,
                            minInDegree: 1,
                            topn: topN
                        };
                        return [4 /*yield*/, index_1.default.recommendCalc(recommendQuery)];
                    case 1:
                        recommendations = _a.sent();
                        similarUsers = [];
                        _i = 0, recommendations_1 = recommendations;
                        _a.label = 2;
                    case 2:
                        if (!(_i < recommendations_1.length)) return [3 /*break*/, 5];
                        rec = recommendations_1[_i];
                        return [4 /*yield*/, index_1.default.getVertexInfoById(this.graphName, this.userVertexType, rec.kid)];
                    case 3:
                        userInfo = _a.sent();
                        properties = {};
                        if (userInfo) {
                            try {
                                properties = JSON.parse(userInfo);
                            }
                            catch (e) {
                                console.warn('解析用户信息失败:', e);
                            }
                        }
                        similarUsers.push({
                            id: rec.kid,
                            score: rec.val,
                            properties: properties
                        });
                        _a.label = 4;
                    case 4:
                        _i++;
                        return [3 /*break*/, 2];
                    case 5: return [2 /*return*/, similarUsers.sort(function (a, b) { return b.score - a.score; })];
                    case 6:
                        error_3 = _a.sent();
                        console.error('推荐相似用户失败:', error_3);
                        throw new Error("\u63A8\u8350\u76F8\u4F3C\u7528\u6237\u5931\u8D25: ".concat(error_3));
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 基于共同好友的相似用户推荐
     * @param userId 用户ID
     * @param topN 返回前N个相似用户
     * @returns 相似用户列表
     */
    UserQueryService.prototype.recommendByMutualFriends = function (userId_1) {
        return __awaiter(this, arguments, void 0, function (userId, topN) {
            var mutualQuery, mutualResults, similarUsers, _i, mutualResults_1, result, userInfo, properties, error_4;
            if (topN === void 0) { topN = 10; }
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 6, , 7]);
                        mutualQuery = {
                            graph: this.graphName,
                            edge1: this.relationEdgeType,
                            edge2: this.relationEdgeType,
                            startId: userId,
                            topn: topN,
                            multiFlag: 1,
                            filterStep1: {
                                filters: [],
                                relationType: 0
                            },
                            filterStep2: {
                                filters: [],
                                relationType: 0
                            }
                        };
                        return [4 /*yield*/, index_1.default.similarMutualCalc(mutualQuery)];
                    case 1:
                        mutualResults = _a.sent();
                        similarUsers = [];
                        _i = 0, mutualResults_1 = mutualResults;
                        _a.label = 2;
                    case 2:
                        if (!(_i < mutualResults_1.length)) return [3 /*break*/, 5];
                        result = mutualResults_1[_i];
                        return [4 /*yield*/, index_1.default.getVertexInfoById(this.graphName, this.userVertexType, result.kid)];
                    case 3:
                        userInfo = _a.sent();
                        properties = {};
                        if (userInfo) {
                            try {
                                properties = JSON.parse(userInfo);
                            }
                            catch (e) {
                                console.warn('解析用户信息失败:', e);
                            }
                        }
                        similarUsers.push({
                            id: result.kid,
                            score: result.val,
                            properties: properties
                        });
                        _a.label = 4;
                    case 4:
                        _i++;
                        return [3 /*break*/, 2];
                    case 5: return [2 /*return*/, similarUsers.sort(function (a, b) { return b.score - a.score; })];
                    case 6:
                        error_4 = _a.sent();
                        console.error('基于共同好友推荐失败:', error_4);
                        throw new Error("\u57FA\u4E8E\u5171\u540C\u597D\u53CB\u63A8\u8350\u5931\u8D25: ".concat(error_4));
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 复合查询：根据用户名查找用户并推荐相似用户
     * @param username 用户名
     * @param topN 推荐数量
     * @returns 用户信息和推荐列表
     */
    UserQueryService.prototype.queryUserAndRecommend = function (username_1) {
        return __awaiter(this, arguments, void 0, function (username, topN) {
            var users, user, recommendations, error_5;
            if (topN === void 0) { topN = 10; }
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, this.queryUserByName(username)];
                    case 1:
                        users = _a.sent();
                        if (users.length === 0) {
                            throw new Error("\u672A\u627E\u5230\u7528\u6237: ".concat(username));
                        }
                        user = users[0];
                        return [4 /*yield*/, this.recommendSimilarUsers(user.id, topN)];
                    case 2:
                        recommendations = _a.sent();
                        return [2 /*return*/, {
                                user: user,
                                recommendations: recommendations,
                                totalRecommendations: recommendations.length
                            }];
                    case 3:
                        error_5 = _a.sent();
                        console.error('复合查询失败:', error_5);
                        throw new Error("\u590D\u5408\u67E5\u8BE2\u5931\u8D25: ".concat(error_5));
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return UserQueryService;
}());
exports.UserQueryService = UserQueryService;
// 创建默认实例
exports.userQueryService = new UserQueryService('default');
// 工厂函数
var createUserQueryService = function (graphName, userVertexType, relationEdgeType) {
    return new UserQueryService(graphName, userVertexType, relationEdgeType);
};
exports.createUserQueryService = createUserQueryService;
