"use strict";
// 图数据浏览器组件 - 展示如何使用图服务
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var graph_1 = require("../services/graph");
var GraphDataExplorer = function (_a) {
    var _b = _a.className, className = _b === void 0 ? '' : _b;
    // 状态管理
    var _c = (0, react_1.useState)([]), graphs = _c[0], setGraphs = _c[1];
    var _d = (0, react_1.useState)(''), selectedGraph = _d[0], setSelectedGraph = _d[1];
    var _e = (0, react_1.useState)([]), vertexTypes = _e[0], setVertexTypes = _e[1];
    var _f = (0, react_1.useState)([]), edgeTypes = _f[0], setEdgeTypes = _f[1];
    var _g = (0, react_1.useState)(false), loading = _g[0], setLoading = _g[1];
    var _h = (0, react_1.useState)(''), error = _h[0], setError = _h[1];
    var _j = (0, react_1.useState)(''), pingResult = _j[0], setPingResult = _j[1];
    // 初始化加载
    (0, react_1.useEffect)(function () {
        testPing();
        loadGraphs();
    }, []);
    // 当选择的图改变时，加载图信息
    (0, react_1.useEffect)(function () {
        if (selectedGraph) {
            loadVertexTypes(selectedGraph);
            loadEdgeTypes(selectedGraph);
        }
    }, [selectedGraph]);
    var testPing = function () { return __awaiter(void 0, void 0, void 0, function () {
        var err_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    return [4 /*yield*/, graph_1.default.ping()];
                case 1:
                    _a.sent();
                    setPingResult('✅ 连接成功');
                    return [3 /*break*/, 3];
                case 2:
                    err_1 = _a.sent();
                    setPingResult("\u274C \u8FDE\u63A5\u5931\u8D25: ".concat(err_1));
                    return [3 /*break*/, 3];
                case 3: return [2 /*return*/];
            }
        });
    }); };
    var loadGraphs = function () { return __awaiter(void 0, void 0, void 0, function () {
        var result, graphList, err_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    setLoading(true);
                    setError('');
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, 4, 5]);
                    return [4 /*yield*/, graph_1.default.getGraphs()];
                case 2:
                    result = _a.sent();
                    graphList = Array.isArray(result) ? result : [];
                    setGraphs(graphList);
                    if (graphList.length > 0) {
                        setSelectedGraph(graphList[0]);
                    }
                    return [3 /*break*/, 5];
                case 3:
                    err_2 = _a.sent();
                    setError("Error loading graphs: ".concat(err_2));
                    return [3 /*break*/, 5];
                case 4:
                    setLoading(false);
                    return [7 /*endfinally*/];
                case 5: return [2 /*return*/];
            }
        });
    }); };
    var loadVertexTypes = function (graphName) { return __awaiter(void 0, void 0, void 0, function () {
        var result, vertexTypeList, err_3;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    return [4 /*yield*/, graph_1.default.getVertexTypes(graphName)];
                case 1:
                    result = _a.sent();
                    vertexTypeList = Array.isArray(result) ? result : [];
                    setVertexTypes(vertexTypeList);
                    return [3 /*break*/, 3];
                case 2:
                    err_3 = _a.sent();
                    console.error("Error loading vertex types: ".concat(err_3));
                    return [3 /*break*/, 3];
                case 3: return [2 /*return*/];
            }
        });
    }); };
    var loadEdgeTypes = function (graphName) { return __awaiter(void 0, void 0, void 0, function () {
        var result, edgeTypeList, err_4;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    return [4 /*yield*/, graph_1.default.getEdgeTypes(graphName)];
                case 1:
                    result = _a.sent();
                    edgeTypeList = Array.isArray(result) ? result : [];
                    setEdgeTypes(edgeTypeList);
                    return [3 /*break*/, 3];
                case 2:
                    err_4 = _a.sent();
                    console.error("Error loading edge types: ".concat(err_4));
                    return [3 /*break*/, 3];
                case 3: return [2 /*return*/];
            }
        });
    }); };
    var createNewGraph = function () { return __awaiter(void 0, void 0, void 0, function () {
        var graphName, err_5;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    graphName = prompt('请输入新图的名称:');
                    if (!graphName) return [3 /*break*/, 4];
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, , 4]);
                    return [4 /*yield*/, graph_1.default.createGraph(graphName)];
                case 2:
                    _a.sent();
                    alert('图创建成功!');
                    loadGraphs(); // 重新加载图列表
                    return [3 /*break*/, 4];
                case 3:
                    err_5 = _a.sent();
                    alert("\u521B\u5EFA\u56FE\u5931\u8D25: ".concat(err_5));
                    return [3 /*break*/, 4];
                case 4: return [2 /*return*/];
            }
        });
    }); };
    return (<div className={"p-6 bg-primary-dark rounded-lg shadow-lg border border-gray-800 ".concat(className)}>
      <h2 className="text-spec-header font-bold mb-6 text-text-primary font-arial">图数据浏览器</h2>

      {/* 连接状态 */}
      <div className="mb-4 p-3 bg-gray-900 rounded border border-gray-700">
        <h3 className="font-semibold mb-2 text-text-primary font-arial">连接状态:</h3>
        <p className="text-spec-regular text-text-secondary font-arial">{pingResult || '检测中...'}</p>
      </div>

      {/* 错误信息 */}
      {error && (<div className="mb-4 p-3 bg-primary-orange/10 border border-primary-orange/30 rounded text-primary-orange font-arial">
          {error}
        </div>)}

      {/* 加载状态 */}
      {loading && (<div className="mb-4 p-3 bg-primary-blue/10 border border-primary-blue/30 rounded text-primary-blue font-arial">
          加载中...
        </div>)}

      {/* 图列表 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-spec-body font-semibold text-text-primary font-arial">图列表</h3>
          <button onClick={createNewGraph} className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-arial font-semibold">
            创建新图
          </button>
        </div>

        {graphs.length > 0 ? (<select value={selectedGraph} onChange={function (e) { return setSelectedGraph(e.target.value); }} className="w-full p-2 bg-gray-900 border border-gray-700 rounded text-text-primary focus:border-primary-blue focus:outline-none font-arial">
            {graphs.map(function (graph) { return (<option key={graph} value={graph} className="bg-gray-900 text-text-primary">
                {graph}
              </option>); })}
          </select>) : (<p className="text-text-secondary font-arial">暂无图数据</p>)}
      </div>

      {/* 图信息 */}
      {selectedGraph && (<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 顶点类型 */}
          <div>
            <h4 className="text-spec-regular font-semibold mb-3 text-text-primary font-arial">顶点类型</h4>
            {vertexTypes.length > 0 ? (<ul className="space-y-1">
                {vertexTypes.map(function (type) { return (<li key={type} className="p-2 bg-primary-yellow/10 border border-primary-yellow/30 rounded text-spec-regular text-primary-yellow font-arial">
                    {type}
                  </li>); })}
              </ul>) : (<p className="text-text-secondary text-spec-regular font-arial">暂无顶点类型</p>)}
          </div>

          {/* 边类型 */}
          <div>
            <h4 className="text-spec-regular font-semibold mb-3 text-text-primary font-arial">边类型</h4>
            {edgeTypes.length > 0 ? (<ul className="space-y-1">
                {edgeTypes.map(function (type) { return (<li key={type} className="p-2 bg-primary-orange/10 border border-primary-orange/30 rounded text-spec-regular text-primary-orange font-arial">
                    {type}
                  </li>); })}
              </ul>) : (<p className="text-text-secondary text-spec-regular font-arial">暂无边类型</p>)}
          </div>
        </div>)}

      {/* 操作按钮 */}
      <div className="mt-6 flex space-x-4">
        <button onClick={testPing} className="px-4 py-2 bg-primary-yellow text-primary-dark rounded hover:bg-primary-yellow/80 transition-colors duration-200 font-semibold font-arial text-spec-regular">
          测试连接
        </button>
        <button onClick={loadGraphs} className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-semibold font-arial text-spec-regular">
          刷新图列表
        </button>
      </div>
    </div>);
};
exports.default = GraphDataExplorer;
