"use strict";
// Toast 管理 Hook
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useToast = void 0;
var react_1 = require("react");
var useToast = function () {
    var _a = (0, react_1.useState)([]), toasts = _a[0], setToasts = _a[1];
    var removeToast = (0, react_1.useCallback)(function (id) {
        console.log('移除 Toast:', id);
        setToasts(function (prev) { return prev.filter(function (toast) { return toast.id !== id; }); });
    }, []);
    var showToast = (0, react_1.useCallback)(function (message, type, duration) {
        if (type === void 0) { type = 'success'; }
        if (duration === void 0) { duration = 3000; }
        var id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        var newToast = {
            id: id,
            message: message,
            type: type,
            duration: duration
        };
        setToasts(function (prev) { return __spreadArray(__spreadArray([], prev, true), [newToast], false); });
        // 自动移除
        setTimeout(function () {
            removeToast(id);
        }, duration + 300); // 加上动画时间
    }, [removeToast]);
    var showSuccess = (0, react_1.useCallback)(function (message, duration) {
        showToast(message, 'success', duration);
    }, [showToast]);
    var showInfo = (0, react_1.useCallback)(function (message, duration) {
        showToast(message, 'info', duration);
    }, [showToast]);
    var showWarning = (0, react_1.useCallback)(function (message, duration) {
        showToast(message, 'warning', duration);
    }, [showToast]);
    var showError = (0, react_1.useCallback)(function (message, duration) {
        showToast(message, 'error', duration);
    }, [showToast]);
    return {
        toasts: toasts,
        showToast: showToast,
        showSuccess: showSuccess,
        showInfo: showInfo,
        showWarning: showWarning,
        showError: showError,
        removeToast: removeToast
    };
};
exports.useToast = useToast;
