"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useToast = exports.ToastProvider = void 0;
var react_1 = require("react");
var react_dom_1 = require("react-dom");
// 创建 Context
var ToastContext = (0, react_1.createContext)(null);
// Toast 组件
var ToastItem = function (_a) {
    var toast = _a.toast, onRemove = _a.onRemove;
    var _b = (0, react_1.useState)(false), isVisible = _b[0], setIsVisible = _b[1];
    var _c = (0, react_1.useState)(false), isLeaving = _c[0], setIsLeaving = _c[1];
    // 进入动画
    react_1.default.useEffect(function () {
        var timer = setTimeout(function () { return setIsVisible(true); }, 10);
        return function () { return clearTimeout(timer); };
    }, []);
    // 自动消失
    react_1.default.useEffect(function () {
        if (toast.duration !== 0) {
            var timer_1 = setTimeout(function () {
                handleRemove();
            }, toast.duration || 4000);
            return function () { return clearTimeout(timer_1); };
        }
    }, [toast.duration]);
    var handleRemove = function () {
        setIsLeaving(true);
        setTimeout(function () {
            onRemove(toast.id);
        }, 300);
    };
    var getToastStyles = function () {
        var baseStyles = "flex items-start gap-3 p-4 rounded-lg shadow-lg backdrop-blur-sm border transition-all duration-300 transform";
        var visibilityStyles = isVisible && !isLeaving
            ? "translate-x-0 opacity-100"
            : "translate-x-full opacity-0";
        switch (toast.type) {
            case 'success':
                return "".concat(baseStyles, " ").concat(visibilityStyles, " bg-green-500/90 border-green-400 text-white");
            case 'error':
                return "".concat(baseStyles, " ").concat(visibilityStyles, " bg-red-500/90 border-red-400 text-white");
            case 'warning':
                return "".concat(baseStyles, " ").concat(visibilityStyles, " bg-yellow-500/90 border-yellow-400 text-white");
            case 'info':
                return "".concat(baseStyles, " ").concat(visibilityStyles, " bg-blue-500/90 border-blue-400 text-white");
            default:
                return "".concat(baseStyles, " ").concat(visibilityStyles, " bg-gray-800/90 border-gray-600 text-white");
        }
    };
    var getIcon = function () {
        switch (toast.type) {
            case 'success':
                return (<svg className="w-5 h-5 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
          </svg>);
            case 'error':
                return (<svg className="w-5 h-5 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"/>
          </svg>);
            case 'warning':
                return (<svg className="w-5 h-5 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
          </svg>);
            case 'info':
                return (<svg className="w-5 h-5 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
          </svg>);
            default:
                return null;
        }
    };
    return (<div className={getToastStyles()}>
      {getIcon()}
      <div className="flex-1 min-w-0">
        <div className="font-semibold text-sm" style={{ fontFamily: 'PingFang SC' }}>
          {toast.title}
        </div>
        {toast.message && (<div className="text-sm opacity-90 mt-1" style={{ fontFamily: 'PingFang SC' }}>
            {toast.message}
          </div>)}
        {toast.action && (<button onClick={toast.action.onClick} className="text-sm underline hover:no-underline mt-2 font-medium" style={{ fontFamily: 'PingFang SC' }}>
            {toast.action.label}
          </button>)}
      </div>
      <button onClick={handleRemove} className="flex-shrink-0 ml-2 text-white/70 hover:text-white transition-colors">
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
        </svg>
      </button>
    </div>);
};
// Toast 容器组件
var ToastContainer = function (_a) {
    var toasts = _a.toasts, onRemove = _a.onRemove;
    if (toasts.length === 0)
        return null;
    return (0, react_dom_1.createPortal)(<div className="fixed top-4 right-4 z-[9999] space-y-2 max-w-sm w-full">
      {toasts.map(function (toast) { return (<ToastItem key={toast.id} toast={toast} onRemove={onRemove}/>); })}
    </div>, document.body);
};
// Provider 组件
var ToastProvider = function (_a) {
    var children = _a.children;
    var _b = (0, react_1.useState)([]), toasts = _b[0], setToasts = _b[1];
    var generateId = function () { return "toast-".concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9)); };
    var showToast = (0, react_1.useCallback)(function (toastData) {
        var id = generateId();
        var newToast = __assign({ id: id, duration: 4000 }, toastData);
        setToasts(function (prev) { return __spreadArray(__spreadArray([], prev, true), [newToast], false); });
    }, []);
    var showSuccess = (0, react_1.useCallback)(function (title, message) {
        showToast({ type: 'success', title: title, message: message });
    }, [showToast]);
    var showError = (0, react_1.useCallback)(function (title, message) {
        showToast({ type: 'error', title: title, message: message });
    }, [showToast]);
    var showWarning = (0, react_1.useCallback)(function (title, message) {
        showToast({ type: 'warning', title: title, message: message });
    }, [showToast]);
    var showInfo = (0, react_1.useCallback)(function (title, message) {
        showToast({ type: 'info', title: title, message: message });
    }, [showToast]);
    var removeToast = (0, react_1.useCallback)(function (id) {
        setToasts(function (prev) { return prev.filter(function (toast) { return toast.id !== id; }); });
    }, []);
    var clearAllToasts = (0, react_1.useCallback)(function () {
        setToasts([]);
    }, []);
    var contextValue = {
        showToast: showToast,
        showSuccess: showSuccess,
        showError: showError,
        showWarning: showWarning,
        showInfo: showInfo,
        removeToast: removeToast,
        clearAllToasts: clearAllToasts,
    };
    return (<ToastContext.Provider value={contextValue}>
      {children}
      <ToastContainer toasts={toasts} onRemove={removeToast}/>
    </ToastContext.Provider>);
};
exports.ToastProvider = ToastProvider;
// Hook
var useToast = function () {
    var context = (0, react_1.useContext)(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
};
exports.useToast = useToast;
