"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useFullscreen = void 0;
var react_1 = require("react");
var useFullscreen = function () {
    var _a = (0, react_1.useState)(false), isFullscreen = _a[0], setIsFullscreen = _a[1];
    var elementRef = (0, react_1.useRef)(null);
    elementRef.current = document.getElementById("container");
    // 进入全屏
    var enterFullscreen = (0, react_1.useCallback)(function () {
        var element = elementRef.current;
        if (!element)
            return;
        try {
            if (element.requestFullscreen) {
                element.requestFullscreen();
            }
            else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen(); // Safari
            }
            else if (element.msRequestFullscreen) {
                element.msRequestFullscreen(); // IE11
            }
        }
        catch (error) {
            console.error("进入全屏失败:", error);
        }
    }, []);
    // 退出全屏
    var exitFullscreen = (0, react_1.useCallback)(function () {
        try {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
            else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen(); // Safari
            }
            else if (document.msExitFullscreen) {
                document.msExitFullscreen(); // IE11
            }
        }
        catch (error) {
            console.error("退出全屏失败:", error);
        }
    }, []);
    // 切换全屏状态
    var toggleFullscreen = (0, react_1.useCallback)(function () {
        if (isFullscreen) {
            exitFullscreen();
        }
        else {
            enterFullscreen();
        }
    }, [isFullscreen, enterFullscreen, exitFullscreen]);
    // 监听全屏状态变化
    (0, react_1.useEffect)(function () {
        var handleFullscreenChange = function () {
            // 检查当前全屏元素是否是目标元素
            var fullscreenElement = document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
            setIsFullscreen(fullscreenElement === elementRef.current);
        };
        // 添加事件监听器
        document.addEventListener("fullscreenchange", handleFullscreenChange);
        document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
        document.addEventListener("msfullscreenchange", handleFullscreenChange);
        // 清理函数
        return function () {
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
            document.removeEventListener("webkitfullscreenchange", handleFullscreenChange);
            document.removeEventListener("msfullscreenchange", handleFullscreenChange);
        };
    }, []);
    return {
        isFullscreen: isFullscreen,
        enterFullscreen: enterFullscreen,
        exitFullscreen: exitFullscreen,
        toggleFullscreen: toggleFullscreen,
    };
};
exports.useFullscreen = useFullscreen;
